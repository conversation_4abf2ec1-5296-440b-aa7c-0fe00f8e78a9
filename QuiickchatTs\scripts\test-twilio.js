#!/usr/bin/env node

/**
 * Twilio SMS Service Diagnostic Script
 * This script tests the Twilio SMS functionality independently
 */

require('dotenv').config();
const twilio = require('twilio');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testTwilioService() {
  log('blue', '🔍 Starting Twilio SMS Service Diagnostic...\n');

  // Step 1: Check environment variables
  log('yellow', '1. Checking Environment Variables:');
  const accountSid = process.env.TWILIO_ACCOUNT_SID;
  const authToken = process.env.TWILIO_AUTH_TOKEN;
  const fromNumber = process.env.TWILIO_FROM_NUMBER;
  const enabled = process.env.TWILIO_ENABLED;

  console.log(`   TWILIO_ACCOUNT_SID: ${accountSid ? '✅ Set' : '❌ Missing'}`);
  console.log(`   TWILIO_AUTH_TOKEN: ${authToken ? '✅ Set' : '❌ Missing'}`);
  console.log(`   TWILIO_FROM_NUMBER: ${fromNumber || '❌ Missing'}`);
  console.log(`   TWILIO_ENABLED: ${enabled || '❌ Missing'}\n`);

  if (!accountSid || !authToken || !fromNumber) {
    log('red', '❌ Missing required Twilio environment variables!');
    return;
  }

  if (enabled !== 'true') {
    log('yellow', '⚠️  Twilio is disabled (TWILIO_ENABLED != true)');
    return;
  }

  // Step 2: Initialize Twilio client
  log('yellow', '2. Initializing Twilio Client:');
  let client;
  try {
    client = twilio(accountSid, authToken);
    log('green', '   ✅ Twilio client initialized successfully\n');
  } catch (error) {
    log('red', `   ❌ Failed to initialize Twilio client: ${error.message}\n`);
    return;
  }

  // Step 3: Validate credentials by fetching account info
  log('yellow', '3. Validating Twilio Credentials:');
  try {
    const account = await client.api.accounts(accountSid).fetch();
    log('green', `   ✅ Account validated: ${account.friendlyName}`);
    log('green', `   ✅ Account Status: ${account.status}`);
    log('green', `   ✅ Account Type: ${account.type}\n`);
  } catch (error) {
    log('red', `   ❌ Invalid credentials: ${error.message}`);
    log('red', `   Error Code: ${error.code || 'Unknown'}\n`);
    return;
  }

  // Step 4: Validate phone number
  log('yellow', '4. Validating From Phone Number:');
  try {
    const phoneNumbers = await client.incomingPhoneNumbers.list();
    const ownedNumber = phoneNumbers.find(num => num.phoneNumber === fromNumber);
    
    if (ownedNumber) {
      log('green', `   ✅ Phone number ${fromNumber} is owned by your account`);
      log('green', `   ✅ Number capabilities: SMS=${ownedNumber.capabilities.sms}, Voice=${ownedNumber.capabilities.voice}\n`);
    } else {
      log('red', `   ❌ Phone number ${fromNumber} is not owned by your account`);
      log('yellow', '   Available numbers in your account:');
      phoneNumbers.forEach(num => {
        console.log(`     - ${num.phoneNumber} (SMS: ${num.capabilities.sms})`);
      });
      console.log();
    }
  } catch (error) {
    log('red', `   ❌ Error validating phone number: ${error.message}\n`);
  }

  // Step 5: Check account balance
  log('yellow', '5. Checking Account Balance:');
  try {
    const balance = await client.balance.fetch();
    log('green', `   ✅ Account Balance: ${balance.balance} ${balance.currency}`);
    
    if (parseFloat(balance.balance) <= 0) {
      log('red', '   ⚠️  Warning: Account balance is low or zero!');
    }
    console.log();
  } catch (error) {
    log('red', `   ❌ Error fetching balance: ${error.message}\n`);
  }

  // Step 6: Test SMS sending (optional)
  const testPhone = process.argv[2];
  if (testPhone) {
    log('yellow', `6. Testing SMS Send to ${testPhone}:`);
    try {
      const message = await client.messages.create({
        body: 'Test message from QuickChat Twilio diagnostic script',
        from: fromNumber,
        to: testPhone
      });

      log('green', `   ✅ SMS sent successfully!`);
      log('green', `   ✅ Message SID: ${message.sid}`);
      log('green', `   ✅ Status: ${message.status}`);
      log('green', `   ✅ Direction: ${message.direction}\n`);
    } catch (error) {
      log('red', `   ❌ SMS send failed: ${error.message}`);
      log('red', `   Error Code: ${error.code || 'Unknown'}`);
      
      // Common error codes
      if (error.code === 21211) {
        log('yellow', '   💡 Tip: Check phone number format (use +**********)');
      } else if (error.code === 21614) {
        log('yellow', '   💡 Tip: Phone number is not a valid mobile number');
      } else if (error.code === 21408) {
        log('yellow', '   💡 Tip: Permission denied - check account permissions');
      } else if (error.code === 20003) {
        log('yellow', '   💡 Tip: Authentication error - check credentials');
      }
      console.log();
    }
  } else {
    log('yellow', '6. SMS Test Skipped (no phone number provided)');
    log('blue', '   To test SMS sending, run: node scripts/test-twilio.js +**********\n');
  }

  // Step 7: Recent messages check
  log('yellow', '7. Checking Recent Messages:');
  try {
    const messages = await client.messages.list({ limit: 5 });
    if (messages.length > 0) {
      log('green', `   ✅ Found ${messages.length} recent messages:`);
      messages.forEach((msg, index) => {
        console.log(`     ${index + 1}. ${msg.to} - ${msg.status} - ${msg.dateCreated}`);
      });
    } else {
      log('yellow', '   ⚠️  No recent messages found');
    }
    console.log();
  } catch (error) {
    log('red', `   ❌ Error fetching recent messages: ${error.message}\n`);
  }

  log('green', '🎉 Twilio diagnostic completed!');
}

// Run the diagnostic
testTwilioService().catch(error => {
  log('red', `💥 Diagnostic failed: ${error.message}`);
  process.exit(1);
});
