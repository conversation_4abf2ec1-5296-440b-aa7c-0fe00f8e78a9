import { logger } from '../utils/logger';
import { ValidationError } from '../utils/errors';
import { QRSession } from '../models/qr-session.model';
import { DeviceLinkSession } from '../models/device-link.model';

interface DeepLinkConfig {
  scheme: string;
  host: string;
  fallbackUrl: string;
}

interface DeepLinkData {
  action: 'qr_scan' | 'device_link' | 'login_approve' | 'device_verify';
  sessionId: string;
  challenge?: string;
  nonce?: string;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

interface UniversalLinkData {
  url: string;
  iosUrl: string;
  androidUrl: string;
  webFallback: string;
}

export class DeepLinkService {
  private static readonly config: DeepLinkConfig = {
    scheme: process.env.DEEP_LINK_SCHEME || 'quickchat',
    host: process.env.DEEP_LINK_HOST || 'auth',
    fallbackUrl: process.env.WEB_FALLBACK_URL || 'https://quickchat.app'
  };

  static generateQRScanDeepLink(sessionId: string, challenge: string, nonce: string): UniversalLinkData {
    const deepLinkData: DeepLinkData = {
      action: 'qr_scan',
      sessionId,
      challenge,
      nonce
    };

    const encodedData = this.encodeDeepLinkData(deepLinkData);
    
    const baseUrl = `${this.config.scheme}://${this.config.host}/qr-scan`;
    const webUrl = `${this.config.fallbackUrl}/qr-scan`;

    return {
      url: `${baseUrl}?data=${encodedData}`,
      iosUrl: `${baseUrl}?data=${encodedData}&platform=ios`,
      androidUrl: `${baseUrl}?data=${encodedData}&platform=android`,
      webFallback: `${webUrl}?session=${sessionId}&challenge=${challenge}&nonce=${nonce}`
    };
  }

  static generateDeviceLinkDeepLink(sessionId: string, verificationCode: string): UniversalLinkData {
    const deepLinkData: DeepLinkData = {
      action: 'device_link',
      sessionId,
      metadata: {
        verificationCode
      }
    };

    const encodedData = this.encodeDeepLinkData(deepLinkData);
    
    const baseUrl = `${this.config.scheme}://${this.config.host}/device-link`;
    const webUrl = `${this.config.fallbackUrl}/device-link`;

    return {
      url: `${baseUrl}?data=${encodedData}`,
      iosUrl: `${baseUrl}?data=${encodedData}&platform=ios`,
      androidUrl: `${baseUrl}?data=${encodedData}&platform=android`,
      webFallback: `${webUrl}?session=${sessionId}&code=${verificationCode}`
    };
  }

  static generateLoginApprovalDeepLink(sessionId: string, deviceInfo: any): UniversalLinkData {
    const deepLinkData: DeepLinkData = {
      action: 'login_approve',
      sessionId,
      metadata: {
        deviceName: deviceInfo.deviceName,
        deviceType: deviceInfo.deviceType,
        location: deviceInfo.location
      }
    };

    const encodedData = this.encodeDeepLinkData(deepLinkData);
    
    const baseUrl = `${this.config.scheme}://${this.config.host}/login-approve`;
    const webUrl = `${this.config.fallbackUrl}/login-approve`;

    return {
      url: `${baseUrl}?data=${encodedData}`,
      iosUrl: `${baseUrl}?data=${encodedData}&platform=ios`,
      androidUrl: `${baseUrl}?data=${encodedData}&platform=android`,
      webFallback: `${webUrl}?session=${sessionId}`
    };
  }

  static generateDeviceVerificationDeepLink(sessionId: string, deviceData: any): UniversalLinkData {
    const deepLinkData: DeepLinkData = {
      action: 'device_verify',
      sessionId,
      metadata: {
        deviceName: deviceData.deviceName,
        deviceType: deviceData.deviceType,
        ipAddress: deviceData.ipAddress
      }
    };

    const encodedData = this.encodeDeepLinkData(deepLinkData);
    
    const baseUrl = `${this.config.scheme}://${this.config.host}/device-verify`;
    const webUrl = `${this.config.fallbackUrl}/device-verify`;

    return {
      url: `${baseUrl}?data=${encodedData}`,
      iosUrl: `${baseUrl}?data=${encodedData}&platform=ios`,
      androidUrl: `${baseUrl}?data=${encodedData}&platform=android`,
      webFallback: `${webUrl}?session=${sessionId}`
    };
  }

  static async parseDeepLink(url: string): Promise<DeepLinkData> {
    try {
      const urlObj = new URL(url);
      
      if (urlObj.protocol !== `${this.config.scheme}:`) {
        throw new ValidationError('Invalid deep link scheme');
      }

      if (urlObj.hostname !== this.config.host) {
        throw new ValidationError('Invalid deep link host');
      }

      const encodedData = urlObj.searchParams.get('data');
      if (!encodedData) {
        throw new ValidationError('Missing deep link data');
      }

      const decodedData = this.decodeDeepLinkData(encodedData);
      
      await this.validateDeepLinkData(decodedData);

      return decodedData;
    } catch (error) {
      logger.error('Failed to parse deep link:', error);
      throw new ValidationError('Invalid deep link format');
    }
  }

  static async validateQRScanDeepLink(sessionId: string): Promise<{ isValid: boolean; session?: any }> {
    try {
      const session = await QRSession.findOne({
        session_id: sessionId,
        status: 'pending'
      });

      if (!session) {
        return { isValid: false };
      }

      if (session.expires_at < new Date()) {
        session.status = 'expired';
        await session.save();
        return { isValid: false };
      }

      return { isValid: true, session };
    } catch (error) {
      logger.error('Failed to validate QR scan deep link:', error);
      return { isValid: false };
    }
  }

  static async validateDeviceLinkDeepLink(sessionId: string): Promise<{ isValid: boolean; session?: any }> {
    try {
      const session = await DeviceLinkSession.findOne({
        session_id: sessionId,
        status: { $in: ['pending', 'verified'] }
      });

      if (!session) {
        return { isValid: false };
      }

      if (session.expires_at < new Date()) {
        session.status = 'expired';
        await session.save();
        return { isValid: false };
      }

      return { isValid: true, session };
    } catch (error) {
      logger.error('Failed to validate device link deep link:', error);
      return { isValid: false };
    }
  }

  static generateAppStoreLinks(): { ios: string; android: string } {
    return {
      ios: process.env.IOS_APP_STORE_URL || 'https://apps.apple.com/app/quickchat',
      android: process.env.ANDROID_PLAY_STORE_URL || 'https://play.google.com/store/apps/details?id=com.quickchat'
    };
  }

  static generateSmartBanner(sessionId: string, action: string): string {
    const appStoreLinks = this.generateAppStoreLinks();
    
    return `
      <meta name="apple-itunes-app" content="app-id=123456789, app-argument=${this.config.scheme}://${this.config.host}/${action}?session=${sessionId}">
      <meta name="google-play-app" content="app-id=com.quickchat, app-argument=${this.config.scheme}://${this.config.host}/${action}?session=${sessionId}">
      <link rel="alternate" href="android-app://com.quickchat/${this.config.scheme}/${this.config.host}/${action}?session=${sessionId}">
    `;
  }

  static generateUniversalLinkHTML(deepLinkData: UniversalLinkData, title: string, description: string): string {
    const appStoreLinks = this.generateAppStoreLinks();
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>${title}</title>
        <meta name="description" content="${description}">
        
        <!-- iOS Smart App Banner -->
        <meta name="apple-itunes-app" content="app-id=123456789, app-argument=${deepLinkData.iosUrl}">
        
        <!-- Android Intent -->
        <meta name="google-play-app" content="app-id=com.quickchat, app-argument=${deepLinkData.androidUrl}">
        
        <script>
          function openApp() {
            const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            
            if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
              // iOS
              window.location = '${deepLinkData.iosUrl}';
              setTimeout(function() {
                window.location = '${appStoreLinks.ios}';
              }, 2000);
            } else if (/android/i.test(userAgent)) {
              // Android
              window.location = '${deepLinkData.androidUrl}';
              setTimeout(function() {
                window.location = '${appStoreLinks.android}';
              }, 2000);
            } else {
              // Desktop/Web
              window.location = '${deepLinkData.webFallback}';
            }
          }
          
          // Auto-redirect after 1 second
          setTimeout(openApp, 1000);
        </script>
      </head>
      <body>
        <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
          <h1>${title}</h1>
          <p>${description}</p>
          <p>Redirecting to QuickChat app...</p>
          <button onclick="openApp()" style="padding: 10px 20px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Open QuickChat
          </button>
          <p style="margin-top: 20px;">
            <a href="${deepLinkData.webFallback}">Continue in browser</a>
          </p>
        </div>
      </body>
      </html>
    `;
  }

  private static encodeDeepLinkData(data: DeepLinkData): string {
    const jsonString = JSON.stringify(data);
    return Buffer.from(jsonString).toString('base64url');
  }

  private static decodeDeepLinkData(encodedData: string): DeepLinkData {
    try {
      const jsonString = Buffer.from(encodedData, 'base64url').toString('utf8');
      return JSON.parse(jsonString);
    } catch (error) {
      throw new ValidationError('Invalid deep link data encoding');
    }
  }

  private static async validateDeepLinkData(data: DeepLinkData): Promise<void> {
    if (!data.action || !data.sessionId) {
      throw new ValidationError('Missing required deep link fields');
    }

    const validActions = ['qr_scan', 'device_link', 'login_approve', 'device_verify'];
    if (!validActions.includes(data.action)) {
      throw new ValidationError('Invalid deep link action');
    }

    if (data.expiresAt && new Date(data.expiresAt) < new Date()) {
      throw new ValidationError('Deep link has expired');
    }
  }
}
