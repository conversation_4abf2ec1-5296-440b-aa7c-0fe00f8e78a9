import mongoose from 'mongoose';
import { logger } from '../utils/logger';

const createIndexes = async () => {
  try {
    const db = mongoose.connection.db;

    if (!db) {
      throw new Error('Database connection not established');
    }

    logger.info('Creating database indexes for performance optimization...');

    // Status collection indexes
    await db.collection('statuses').createIndex(
      { user_id: 1, is_active: 1, expires_at: 1 },
      { name: 'status_user_active_expires' }
    );
    
    await db.collection('statuses').createIndex(
      { user_id: 1, createdAt: -1 },
      { name: 'status_user_created' }
    );
    
    await db.collection('statuses').createIndex(
      { is_active: 1, expires_at: 1, createdAt: -1 },
      { name: 'status_active_expires_created' }
    );

    // StatusView collection indexes
    await db.collection('statusviews').createIndex(
      { status_id: 1, viewer_id: 1 },
      { name: 'statusview_status_viewer', unique: true }
    );
    
    await db.collection('statusviews').createIndex(
      { status_id: 1, viewed_at: -1 },
      { name: 'statusview_status_viewed' }
    );
    
    await db.collection('statusviews').createIndex(
      { viewer_id: 1, viewed_at: -1 },
      { name: 'statusview_viewer_viewed' }
    );

    // User collection indexes
    await db.collection('users').createIndex(
      { phone: 1 },
      { name: 'user_phone', unique: true }
    );
    
    await db.collection('users').createIndex(
      { email: 1 },
      { name: 'user_email', sparse: true }
    );
    
    await db.collection('users').createIndex(
      { username: 1 },
      { name: 'user_username', sparse: true }
    );
    
    await db.collection('users').createIndex(
      { last_seen: -1 },
      { name: 'user_last_seen' }
    );

    // UserContacts collection indexes
    await db.collection('usercontacts').createIndex(
      { user_id: 1 },
      { name: 'usercontacts_user', unique: true }
    );
    
    await db.collection('usercontacts').createIndex(
      { 'contacts.phone_number': 1 },
      { name: 'usercontacts_phone' }
    );
    
    await db.collection('usercontacts').createIndex(
      { 'contacts.display_name': 1 },
      { name: 'usercontacts_display_name' }
    );
    
    await db.collection('usercontacts').createIndex(
      { 'contacts.is_registered': 1 },
      { name: 'usercontacts_registered' }
    );

    // Verification collection indexes
    await db.collection('verifications').createIndex(
      { phone: 1 },
      { name: 'verification_phone', unique: true }
    );
    
    await db.collection('verifications').createIndex(
      { expires_at: 1 },
      { name: 'verification_expires', expireAfterSeconds: 0 }
    );

    // QR Auth collection indexes (if exists)
    await db.collection('qrauths').createIndex(
      { session_id: 1 },
      { name: 'qrauth_session', unique: true }
    );
    
    await db.collection('qrauths').createIndex(
      { expires_at: 1 },
      { name: 'qrauth_expires', expireAfterSeconds: 0 }
    );

    // Device collection indexes (if exists)
    await db.collection('devices').createIndex(
      { user_id: 1, device_id: 1 },
      { name: 'device_user_device', unique: true }
    );
    
    await db.collection('devices').createIndex(
      { user_id: 1, is_active: 1 },
      { name: 'device_user_active' }
    );

    // Compound indexes for complex queries
    await db.collection('statuses').createIndex(
      { user_id: 1, privacy_setting: 1, is_active: 1, expires_at: 1 },
      { name: 'status_user_privacy_active_expires' }
    );

    await db.collection('statusviews').createIndex(
      { viewer_id: 1, status_id: 1, viewed_at: -1 },
      { name: 'statusview_viewer_status_viewed' }
    );

    logger.info('Database indexes created successfully');
    
    // List all indexes for verification
    const collections = ['statuses', 'statusviews', 'users', 'usercontacts', 'verifications'];
    for (const collectionName of collections) {
      try {
        const indexes = await db.collection(collectionName).listIndexes().toArray();
        logger.info(`${collectionName} indexes:`, indexes.map(idx => idx.name));
      } catch (error) {
        logger.warn(`Collection ${collectionName} not found or error listing indexes`);
      }
    }

  } catch (error) {
    logger.error('Error creating indexes:', error);
    throw error;
  }
};

export { createIndexes };

// Run if called directly
if (require.main === module) {
  const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/quiickchat';
  
  mongoose.connect(mongoUri)
    .then(() => {
      logger.info('Connected to MongoDB');
      return createIndexes();
    })
    .then(() => {
      logger.info('Index creation completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Index creation failed:', error);
      process.exit(1);
    });
}
