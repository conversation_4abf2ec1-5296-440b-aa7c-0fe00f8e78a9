import * as libsignal from '@signalapp/libsignal-client';
import sodium from 'sodium-native';
import { logger } from '../utils/logger';
import { ValidationError } from '../utils/errors';

export interface KeyPair {
  publicKey: Uint8Array;
  privateKey: Uint8Array;
}

export interface PreKeyBundle {
  registrationId: number;
  deviceId: number;
  preKeyId: number;
  preKeyPublic: Uint8Array;
  signedPreKeyId: number;
  signedPreKeyPublic: Uint8Array;
  signedPreKeySignature: Uint8Array;
  identityKey: Uint8Array;
}

export interface X3DHResult {
  sharedSecret: Uint8Array;
  associatedData: Uint8Array;
}

export class CryptoService {
  
  static generateIdentityKeyPair(): KeyPair {
    const privateKey = libsignal.PrivateKey.generate();
    const publicKey = privateKey.getPublicKey();
    
    return {
      privateKey: privateKey.serialize(),
      publicKey: publicKey.serialize()
    };
  }

  static generatePreKey(keyId: number): { keyId: number; keyPair: KeyPair } {
    const privateKey = libsignal.PrivateKey.generate();
    const publicKey = privateKey.getPublicKey();
    
    return {
      keyId,
      keyPair: {
        privateKey: privateKey.serialize(),
        publicKey: publicKey.serialize()
      }
    };
  }

  static generateSignedPreKey(identityPrivateKey: Uint8Array, keyId: number): { keyId: number; keyPair: KeyPair; signature: Uint8Array } {
    const privateKey = libsignal.PrivateKey.generate();
    const publicKey = privateKey.getPublicKey();
    
    const identityKey = libsignal.PrivateKey.deserialize(identityPrivateKey);
    const signature = identityKey.sign(publicKey.serialize());
    
    return {
      keyId,
      keyPair: {
        privateKey: privateKey.serialize(),
        publicKey: publicKey.serialize()
      },
      signature
    };
  }

  static generateRegistrationId(): number {
    const buffer = Buffer.allocUnsafe(4);
    sodium.randombytes_buf(buffer);
    return buffer.readUInt32BE(0) & 0x3fff;
  }

  static performX3DH(
    identityKeyPair: KeyPair,
    ephemeralKeyPair: KeyPair,
    remoteIdentityKey: Uint8Array,
    remoteSignedPreKey: Uint8Array,
    remoteOneTimePreKey?: Uint8Array
  ): X3DHResult {
    try {
      const identityPrivate = libsignal.PrivateKey.deserialize(identityKeyPair.privateKey);
      const ephemeralPrivate = libsignal.PrivateKey.deserialize(ephemeralKeyPair.privateKey);
      const remoteIdentity = libsignal.PublicKey.deserialize(remoteIdentityKey);
      const remoteSignedPre = libsignal.PublicKey.deserialize(remoteSignedPreKey);

      const dh1 = identityPrivate.agree(remoteSignedPre);
      const dh2 = ephemeralPrivate.agree(remoteIdentity);
      const dh3 = ephemeralPrivate.agree(remoteSignedPre);

      let dhInputs = [dh1, dh2, dh3];

      if (remoteOneTimePreKey) {
        const remoteOneTime = libsignal.PublicKey.deserialize(remoteOneTimePreKey);
        const dh4 = ephemeralPrivate.agree(remoteOneTime);
        dhInputs.push(dh4);
      }

      const sharedSecret = this.deriveSharedSecret(dhInputs);
      
      const associatedData = Buffer.concat([
        identityKeyPair.publicKey,
        remoteIdentityKey
      ]);

      return {
        sharedSecret,
        associatedData
      };
    } catch (error) {
      logger.error('X3DH key agreement failed:', error);
      throw new ValidationError('Key agreement failed');
    }
  }

  static deriveRootKey(sharedSecret: Uint8Array): Uint8Array {
    const salt = Buffer.from('WhisperText', 'utf8');
    const info = Buffer.from('WhisperRatchet', 'utf8');
    
    return this.hkdf(sharedSecret, salt, info, 32);
  }

  static deriveChainKey(rootKey: Uint8Array, dhOutput: Uint8Array): { rootKey: Uint8Array; chainKey: Uint8Array } {
    const salt = rootKey;
    const info = Buffer.from('WhisperRatchet', 'utf8');
    
    const output = this.hkdf(dhOutput, salt, info, 64);
    
    return {
      rootKey: output.subarray(0, 32),
      chainKey: output.subarray(32, 64)
    };
  }

  static deriveMessageKey(chainKey: Uint8Array): { chainKey: Uint8Array; messageKey: Uint8Array } {
    const messageKey = this.hmacSha256(chainKey, Buffer.from([0x01]));
    const nextChainKey = this.hmacSha256(chainKey, Buffer.from([0x02]));
    
    return {
      chainKey: nextChainKey,
      messageKey
    };
  }

  static encryptMessage(messageKey: Uint8Array, plaintext: Uint8Array, associatedData: Uint8Array): { ciphertext: Uint8Array; tag: Uint8Array } {
    const keys = this.deriveEncryptionKeys(messageKey);

    const nonce = Buffer.allocUnsafe(12);
    sodium.randombytes_buf(nonce);

    const ciphertext = Buffer.allocUnsafe(plaintext.length + 16);

    sodium.crypto_secretbox_easy(
      ciphertext,
      Buffer.from(plaintext),
      nonce,
      Buffer.from(keys.encryptionKey)
    );

    return {
      ciphertext: Buffer.concat([nonce, ciphertext.subarray(0, plaintext.length)]),
      tag: ciphertext.subarray(plaintext.length)
    };
  }

  static decryptMessage(messageKey: Uint8Array, ciphertext: Uint8Array, tag: Uint8Array, associatedData: Uint8Array): Uint8Array {
    const keys = this.deriveEncryptionKeys(messageKey);

    const nonce = ciphertext.subarray(0, 12);
    const actualCiphertext = ciphertext.subarray(12);
    const fullCiphertext = Buffer.concat([Buffer.from(actualCiphertext), Buffer.from(tag)]);

    const plaintext = Buffer.allocUnsafe(actualCiphertext.length);

    const result = sodium.crypto_secretbox_open_easy(
      plaintext,
      fullCiphertext,
      Buffer.from(nonce),
      Buffer.from(keys.encryptionKey)
    );

    if (!result) {
      throw new ValidationError('Message decryption failed');
    }

    return plaintext;
  }

  static generateDeviceFingerprint(identityKey: Uint8Array, deviceInfo: any): string {
    const data = Buffer.concat([
      identityKey,
      Buffer.from(JSON.stringify(deviceInfo), 'utf8')
    ]);
    
    const hash = Buffer.allocUnsafe(32);
    sodium.crypto_generichash(hash, data);
    
    return hash.toString('hex');
  }

  static verifySignature(publicKey: Uint8Array, message: Uint8Array, signature: Uint8Array): boolean {
    try {
      const key = libsignal.PublicKey.deserialize(publicKey);
      return key.verify(message, signature);
    } catch (error) {
      logger.error('Signature verification failed:', error);
      return false;
    }
  }

  private static deriveSharedSecret(dhOutputs: Uint8Array[]): Uint8Array {
    const combinedInput = Buffer.concat(dhOutputs);
    const salt = Buffer.alloc(32, 0);
    const info = Buffer.from('WhisperText', 'utf8');
    
    return this.hkdf(combinedInput, salt, info, 32);
  }

  private static deriveEncryptionKeys(messageKey: Uint8Array): { encryptionKey: Uint8Array; macKey: Uint8Array } {
    const info1 = Buffer.from('WhisperMessageKeys', 'utf8');
    const keys = this.hkdf(messageKey, Buffer.alloc(32, 0), info1, 80);
    
    return {
      encryptionKey: keys.subarray(0, 32),
      macKey: keys.subarray(32, 64)
    };
  }

  private static hkdf(ikm: Uint8Array, salt: Uint8Array, info: Uint8Array, length: number): Uint8Array {
    const prk = this.hmacSha256(salt, ikm);
    
    const output = Buffer.allocUnsafe(length);
    let t = Buffer.alloc(0);
    let counter = 1;
    let offset = 0;
    
    while (offset < length) {
      const input = Buffer.concat([t, info, Buffer.from([counter])]);
      t = Buffer.from(this.hmacSha256(prk, input));
      
      const copyLength = Math.min(t.length, length - offset);
      t.copy(output, offset, 0, copyLength);
      offset += copyLength;
      counter++;
    }
    
    return output;
  }

  private static hmacSha256(key: Uint8Array, data: Uint8Array): Uint8Array {
    const output = Buffer.allocUnsafe(32);
    sodium.crypto_auth(output, Buffer.from(data), Buffer.from(key));
    return output;
  }

  static generateSecureRandom(length: number): Uint8Array {
    const buffer = Buffer.allocUnsafe(length);
    sodium.randombytes_buf(buffer);
    return buffer;
  }

  static constantTimeCompare(a: Uint8Array, b: Uint8Array): boolean {
    if (a.length !== b.length) {
      return false;
    }

    return sodium.sodium_memcmp(Buffer.from(a), Buffer.from(b));
  }
}
