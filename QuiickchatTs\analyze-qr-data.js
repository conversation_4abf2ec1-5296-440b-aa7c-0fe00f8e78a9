#!/usr/bin/env node

/**
 * QuickChat QR Data Analyzer
 * 
 * This script analyzes the JSON data structure inside QuickChat QR codes
 * and provides detailed information about the authentication session.
 * 
 * Usage:
 *   node analyze-qr-data.js                                    # Analyze sample QR data
 *   node analyze-qr-data.js '{"v":"1.0","t":"login",...}'      # Analyze custom QR data
 */

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(level, message, data = null) {
  const colorMap = {
    info: colors.cyan,
    success: colors.green,
    warn: colors.yellow,
    error: colors.red,
    debug: colors.magenta
  };
  const color = colorMap[level] || colors.reset;
  console.log(`${color}${message}${colors.reset}`);
  
  if (data) {
    console.log(`${colors.bright}${JSON.stringify(data, null, 2)}${colors.reset}`);
  }
}

// Sample QR data that would be inside your QR code
// This is what the QR code contains as JSON
const sampleQRData = {
  "v": "1.0",
  "t": "login", 
  "s": "session-abc123def456",
  "c": "challenge-xyz789uvw012",
  "n": "nonce-mno345pqr678",
  "e": Math.floor(Date.now() / 1000) + 120 // Expires in 2 minutes
};

function analyzeQRData(qrData) {
  log('info', '\n📊 QuickChat QR Code Data Analysis');
  log('info', '=====================================');
  
  try {
    let parsedData;
    if (typeof qrData === 'string') {
      parsedData = JSON.parse(qrData);
    } else {
      parsedData = qrData;
    }
    
    log('success', '✅ QR data is valid JSON');
    log('info', '\n📋 Raw QR Data:', parsedData);
    
    // Analyze each field in detail
    log('info', '\n🔍 Detailed Field Analysis:');
    
    // Version Analysis
    if (parsedData.v) {
      log('info', `\n📋 Version Field (v): "${parsedData.v}"`);
      log('debug', '   Purpose: Protocol version for compatibility');
      log('debug', '   Current: 1.0 (latest)');
      if (parsedData.v === '1.0') {
        log('success', '   ✅ Using current protocol version');
      } else {
        log('warn', '   ⚠️  Using older or unknown protocol version');
      }
    }
    
    // Type Analysis
    if (parsedData.t) {
      log('info', `\n🎯 Type Field (t): "${parsedData.t}"`);
      log('debug', '   Purpose: Defines the authentication type');
      
      const typeInfo = {
        'login': {
          description: 'User Login Authentication',
          purpose: 'Authenticate user on new device/browser',
          flow: 'Desktop → QR → Mobile Scan → Mobile Approve → Desktop Login'
        },
        'device_link': {
          description: 'Device Linking/Pairing',
          purpose: 'Link new device to existing account',
          flow: 'New Device → QR → Mobile Scan → Mobile Approve → Device Linked'
        }
      };
      
      const info = typeInfo[parsedData.t];
      if (info) {
        log('success', `   ✅ ${info.description}`);
        log('debug', `   Purpose: ${info.purpose}`);
        log('debug', `   Flow: ${info.flow}`);
      } else {
        log('warn', '   ⚠️  Unknown authentication type');
      }
    }
    
    // Session ID Analysis
    if (parsedData.s) {
      log('info', `\n🆔 Session ID Field (s): "${parsedData.s}"`);
      log('debug', '   Purpose: Unique identifier for this authentication session');
      log('debug', `   Length: ${parsedData.s.length} characters`);
      log('debug', `   Format: ${parsedData.s.includes('-') ? 'Hyphenated' : 'Continuous'}`);
      
      if (parsedData.s.length >= 20) {
        log('success', '   ✅ Adequate length for security');
      } else {
        log('warn', '   ⚠️  Session ID might be too short');
      }
      
      if (parsedData.s.startsWith('session-')) {
        log('success', '   ✅ Standard session ID format');
      } else {
        log('debug', '   ℹ️  Custom session ID format');
      }
    }
    
    // Challenge Analysis
    if (parsedData.c) {
      log('info', `\n🔐 Challenge Field (c): "${parsedData.c}"`);
      log('debug', '   Purpose: Cryptographic challenge for security verification');
      log('debug', `   Length: ${parsedData.c.length} characters`);
      log('debug', '   Security: Prevents unauthorized access attempts');
      
      if (parsedData.c.length >= 16) {
        log('success', '   ✅ Strong cryptographic challenge');
      } else {
        log('warn', '   ⚠️  Challenge might be too weak');
      }
    }
    
    // Nonce Analysis
    if (parsedData.n) {
      log('info', `\n🎲 Nonce Field (n): "${parsedData.n}"`);
      log('debug', '   Purpose: One-time number to prevent replay attacks');
      log('debug', `   Length: ${parsedData.n.length} characters`);
      log('debug', '   Security: Ensures each QR code is unique and single-use');
      
      if (parsedData.n.length >= 8) {
        log('success', '   ✅ Adequate nonce for replay protection');
      } else {
        log('warn', '   ⚠️  Nonce might be too short');
      }
    }
    
    // Expiry Analysis
    if (parsedData.e) {
      log('info', `\n⏰ Expiry Field (e): ${parsedData.e}`);
      log('debug', '   Purpose: Unix timestamp when QR code expires');
      
      const expiryDate = new Date(parsedData.e * 1000);
      const now = new Date();
      const isExpired = expiryDate < now;
      const timeDiff = Math.abs(expiryDate - now) / 1000;
      
      log('debug', `   Unix Timestamp: ${parsedData.e}`);
      log('debug', `   Expiry Date: ${expiryDate.toISOString()}`);
      log('debug', `   Local Time: ${expiryDate.toLocaleString()}`);
      
      if (isExpired) {
        log('error', '   ❌ QR CODE HAS EXPIRED');
        log('debug', `   Expired: ${Math.floor(timeDiff / 60)} minutes ago`);
      } else {
        log('success', '   ✅ QR code is still valid');
        const minutes = Math.floor(timeDiff / 60);
        const seconds = Math.floor(timeDiff % 60);
        log('debug', `   Time remaining: ${minutes}m ${seconds}s`);
        
        if (timeDiff <= 300) { // 5 minutes
          log('success', '   ✅ Appropriate short expiry time');
        } else {
          log('warn', '   ⚠️  Long expiry time (potential security risk)');
        }
      }
    }
    
    // Security Analysis
    log('info', '\n🛡️ Security Analysis:');
    const securityScore = calculateSecurityScore(parsedData);
    
    log('info', `\n📊 Security Score: ${securityScore.score}/100`);
    
    if (securityScore.score >= 80) {
      log('success', '🔒 EXCELLENT security level');
    } else if (securityScore.score >= 60) {
      log('warn', '🔓 GOOD security level');
    } else {
      log('error', '⚠️  WEAK security level');
    }
    
    log('info', '\n✅ Security Strengths:');
    securityScore.strengths.forEach(strength => {
      log('success', `   • ${strength}`);
    });
    
    if (securityScore.issues.length > 0) {
      log('info', '\n⚠️  Security Issues:');
      securityScore.issues.forEach(issue => {
        log('warn', `   • ${issue}`);
      });
    }
    
    // Usage Instructions
    log('info', '\n📱 How to Use This QR Code:');
    
    if (parsedData.t === 'login') {
      log('info', '\n🔑 Login Authentication Process:');
      log('info', '   1. 📱 Open QuickChat mobile app');
      log('info', '   2. 🔍 Tap "Scan QR Code" or use camera');
      log('info', '   3. 📷 Point camera at this QR code');
      log('info', '   4. 👀 Review login request details');
      log('info', '   5. ✅ Tap "Approve" to allow login');
      log('info', '   6. 🎉 Desktop/web client will be logged in');
    } else if (parsedData.t === 'device_link') {
      log('info', '\n🔗 Device Linking Process:');
      log('info', '   1. 📱 Open QuickChat mobile app');
      log('info', '   2. 🔍 Tap "Link Device" or scan QR');
      log('info', '   3. 📷 Point camera at this QR code');
      log('info', '   4. 👀 Review device linking request');
      log('info', '   5. ✅ Tap "Link Device" to approve');
      log('info', '   6. 🔗 New device will be linked to account');
    }
    
    // Technical Details
    log('info', '\n🔧 Technical Implementation:');
    log('debug', '   • QR code contains JSON data structure');
    log('debug', '   • Mobile app parses JSON and validates fields');
    log('debug', '   • Server verifies session ID and challenge');
    log('debug', '   • Cryptographic verification prevents tampering');
    log('debug', '   • Short expiry time limits attack window');
    log('debug', '   • Nonce prevents replay attacks');
    
    return parsedData;
    
  } catch (error) {
    log('error', '❌ Invalid QR data format');
    log('debug', `Error: ${error.message}`);
    log('debug', `Raw data: ${qrData}`);
    return null;
  }
}

function calculateSecurityScore(data) {
  let score = 0;
  const issues = [];
  const strengths = [];
  
  // Version check (10 points)
  if (data.v === '1.0') {
    score += 10;
    strengths.push('Uses current protocol version (1.0)');
  } else if (data.v) {
    score += 5;
    issues.push('Using older or unknown protocol version');
  } else {
    issues.push('No protocol version specified');
  }
  
  // Session ID check (20 points)
  if (data.s && data.s.length >= 20) {
    score += 20;
    strengths.push('Session ID has adequate length for security');
  } else if (data.s && data.s.length >= 10) {
    score += 10;
    issues.push('Session ID could be longer for better security');
  } else {
    issues.push('Session ID too short or missing');
  }
  
  // Challenge check (25 points)
  if (data.c && data.c.length >= 20) {
    score += 25;
    strengths.push('Strong cryptographic challenge present');
  } else if (data.c && data.c.length >= 10) {
    score += 15;
    issues.push('Cryptographic challenge could be stronger');
  } else {
    issues.push('Weak or missing cryptographic challenge');
  }
  
  // Nonce check (20 points)
  if (data.n && data.n.length >= 12) {
    score += 20;
    strengths.push('Strong nonce for replay attack protection');
  } else if (data.n && data.n.length >= 6) {
    score += 10;
    issues.push('Nonce could be longer for better protection');
  } else {
    issues.push('Weak or missing nonce');
  }
  
  // Expiry check (25 points)
  if (data.e) {
    const expiryDate = new Date(data.e * 1000);
    const now = new Date();
    const timeLeft = (expiryDate - now) / 1000;
    
    if (timeLeft > 0 && timeLeft <= 300) { // 5 minutes max
      score += 25;
      strengths.push('Appropriate short expiry time (≤5 minutes)');
    } else if (timeLeft > 0 && timeLeft <= 600) { // 10 minutes
      score += 15;
      issues.push('Expiry time could be shorter for better security');
    } else if (timeLeft > 0) {
      score += 5;
      issues.push('Expiry time too long (security risk)');
    } else {
      issues.push('QR code has expired');
    }
  } else {
    issues.push('No expiry time specified (major security risk)');
  }
  
  return { score, issues, strengths };
}

function main() {
  console.log(`${colors.bright}${colors.blue}🔍 QuickChat QR Data Analyzer${colors.reset}`);
  console.log(`${colors.cyan}Analyzing QuickChat authentication QR data structure${colors.reset}\n`);
  
  const input = process.argv[2];
  let qrData = null;
  
  if (input) {
    if (input.startsWith('{') && input.endsWith('}')) {
      log('info', 'Analyzing provided JSON QR data...');
      qrData = input;
    } else {
      log('error', 'Invalid input. Expected JSON data like: {"v":"1.0","t":"login",...}');
      process.exit(1);
    }
  } else {
    log('info', 'Using sample QR data for demonstration...');
    qrData = sampleQRData;
  }
  
  const parsedData = analyzeQRData(qrData);
  
  if (parsedData) {
    log('info', '\n🎯 Quick Summary:');
    log('success', `   Type: ${parsedData.t || 'Unknown'} authentication`);
    log('success', `   Session: ${parsedData.s || 'Unknown'}`);
    log('success', `   Version: ${parsedData.v || 'Unknown'}`);
    
    if (parsedData.e) {
      const expiryDate = new Date(parsedData.e * 1000);
      const isExpired = expiryDate < new Date();
      log(isExpired ? 'error' : 'success', `   Status: ${isExpired ? 'EXPIRED' : 'Valid'} (expires ${expiryDate.toLocaleString()})`);
    }
    
    log('info', '\n📚 Related Documentation:');
    log('info', '   • QR_AUTHENTICATION_README.md - Complete guide');
    log('info', '   • QR_FLOW_DIAGRAMS.md - Visual flow diagrams');
    log('info', '   • BEGINNER_GUIDE.md - Simple explanation');
  }
}

main();
