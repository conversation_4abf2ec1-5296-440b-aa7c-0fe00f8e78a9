import { FastifyRequest, FastifyReply } from 'fastify';
import * as userService from '../services/user.service';
import { uploadSingleFile } from '../services/cloudinary.service';
import { ValidationError, NotFoundError } from '../utils/errors';
import { formatPhoneNumber } from '../utils/helpers';
import { logger } from '../utils/logger';
import {
  ApiResponse,
  UpdateProfileRequest,
  UserQuery,
  AuthenticatedRequest,
  PaginationQuery,
  PaginatedResponse
} from '../types';

export const getCurrentUser = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  const { phone, user_id } = request.query as UserQuery;

  if (phone) {
    const formattedPhone = formatPhoneNumber(phone);
    
    try {
      const user = await userService.getUserByPhone(formattedPhone);

      if (user._id) {
        await userService.updateLastSeen(user._id.toString());
      }

      return reply.status(200).send({
        success: true,
        message: 'User retrieved successfully',
        data: {
          id: user._id?.toString(),
          email: user.email,
          username: user.username,
          phone: user.phone,
          profile_picture: user.profile_picture,
          is_verified: user.is_verified,
          status: user.status,
          bio: user.bio,
          address: user.address,
          created_at: user.createdAt
        }
      });
    } catch (error: any) {
      logger.error('Error getting user by phone:', error);
      return reply.status(500).send({
        success: false,
        message: 'Failed to retrieve user'
      });
    }
  }

  if (user_id) {
    try {
      const user = await userService.getUserById(user_id);
      await userService.updateLastSeen(user_id);

      return reply.status(200).send({
        success: true,
        message: 'User retrieved successfully',
        data: {
          id: user._id?.toString(),
          email: user.email,
          username: user.username,
          phone: user.phone,
          profile_picture: user.profile_picture,
          is_verified: user.is_verified,
          status: user.status,
          bio: user.bio,
          address: user.address,
          created_at: user.createdAt
        }
      });
    } catch (error: any) {
      logger.error('Error getting user by ID:', error);
      return reply.status(500).send({
        success: false,
        message: 'Failed to retrieve user'
      });
    }
  }

  if (request.user?.phone) {
    try {
      const user = await userService.getUserByPhone(request.user.phone);

      if (user._id) {
        await userService.updateLastSeen(user._id.toString());
      }

      return reply.status(200).send({
        success: true,
        message: 'Current user retrieved successfully',
        data: {
          id: user._id?.toString(),
          email: user.email,
          username: user.username,
          phone: user.phone,
          profile_picture: user.profile_picture,
          is_verified: user.is_verified,
          status: user.status,
          bio: user.bio,
          address: user.address,
          created_at: user.createdAt
        }
      });
    } catch (error: any) {
      logger.error('Error getting current user:', error);
      return reply.status(500).send({
        success: false,
        message: 'Failed to retrieve user'
      });
    }
  }

  return reply.status(400).send({
    success: false,
    message: 'Unable to identify user'
  });
};

export const updateProfile = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const updates = request.body as UpdateProfileRequest;

  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const user = await userService.getUserByPhone(request.user.phone);
    const updatedUser = await userService.updateProfile(user._id.toString(), updates);

    return reply.status(200).send({
      success: true,
      message: 'Profile updated successfully',
      data: {
        id: updatedUser._id?.toString(),
        email: updatedUser.email,
        username: updatedUser.username,
        phone: updatedUser.phone,
        profile_picture: updatedUser.profile_picture,
        is_verified: updatedUser.is_verified,
        status: updatedUser.status,
        bio: updatedUser.bio,
        address: updatedUser.address,
        created_at: updatedUser.createdAt
      }
    });
  } catch (error: any) {
    logger.error('Error updating profile:', error);
    return reply.status(500).send({
      success: false,
      message: 'Profile update failed'
    });
  }
};

export const deleteAccount = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const user = await userService.getUserByPhone(request.user.phone);
    await userService.deleteUser(user._id.toString());

    return reply.status(200).send({
      success: true,
      message: 'Account deleted successfully'
    });
  } catch (error: any) {
    logger.error('Error deleting account:', error);
    return reply.status(500).send({
      success: false,
      message: 'Account deletion failed'
    });
  }
};

export const uploadProfilePicture = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const imageUrl = await uploadSingleFile(request, 'profilePicture');

    if (!imageUrl) {
      return reply.status(400).send({
        success: false,
        message: 'No file uploaded'
      });
    }

    const user = await userService.getUserByPhone(request.user.phone);
    const updatedUser = await userService.updateProfile(user._id.toString(), {
      profile_picture: imageUrl
    });

    return reply.status(200).send({
      success: true,
      message: 'Profile picture uploaded successfully',
      data: {
        profile_picture: imageUrl,
        user: {
          id: updatedUser._id?.toString(),
          email: updatedUser.email,
          username: updatedUser.username,
          phone: updatedUser.phone,
          profile_picture: updatedUser.profile_picture,
          is_verified: updatedUser.is_verified,
          status: updatedUser.status,
          bio: updatedUser.bio,
          address: updatedUser.address,
          created_at: updatedUser.createdAt
        }
      }
    });
  } catch (error: any) {
    logger.error('Error uploading profile picture:', error);
    return reply.status(500).send({
      success: false,
      message: 'Profile picture upload failed'
    });
  }
};
