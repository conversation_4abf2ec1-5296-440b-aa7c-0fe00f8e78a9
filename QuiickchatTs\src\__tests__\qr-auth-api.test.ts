import fastify from 'fastify';
import { qrAuthRoutes } from '../routes/qr-auth.route';
import { deepLinkRoutes } from '../routes/deep-link.route';
import { connectDatabase } from '../config/database';
import { QRSession } from '../models/qr-session.model';
import { User } from '../models/user.model';
import { Auth } from '../middleware/auth.middleware';
import mongoose from 'mongoose';

describe('QR Authentication API', () => {
  let app: any;
  let authToken: string;

  beforeAll(async () => {
    await connectDatabase();
    
    app = fastify();
    await app.register(qrAuthRoutes, { prefix: '/api/v1/qr' });
    await app.register(deepLinkRoutes, { prefix: '/api/v1/link' });

    // Create test user and generate auth token
    const user = new User({
      phone: '+1234567890',
      is_verified: true,
      devices: []
    });
    await user.save();

    authToken = Auth.generateAccessToken('+1234567890');
  });

  afterAll(async () => {
    await app.close();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    await QRSession.deleteMany({});
  });

  describe('POST /api/v1/qr/generate', () => {
    it('should generate QR code for login', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/qr/generate',
        headers: {
          'x-device-fingerprint': 'test-fingerprint-123'
        },
        payload: {
          session_type: 'login',
          device_fingerprint: 'test-fingerprint-123'
        }
      });

      expect(response.statusCode).toBe(201);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data).toHaveProperty('qr_code');
      expect(body.data).toHaveProperty('session_id');
      expect(body.data).toHaveProperty('expires_at');
      expect(body.data.session_type).toBe('login');
    });

    it('should generate QR code for device linking', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/qr/generate',
        headers: {
          'x-device-fingerprint': 'test-fingerprint-123'
        },
        payload: {
          session_type: 'device_link',
          device_fingerprint: 'test-fingerprint-123'
        }
      });

      expect(response.statusCode).toBe(201);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data.session_type).toBe('device_link');
    });

    it('should reject request without device fingerprint', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/qr/generate',
        payload: {
          session_type: 'login',
          device_fingerprint: 'test-fingerprint-123'
        }
      });

      expect(response.statusCode).toBe(400);
    });

    it('should enforce rate limiting', async () => {
      const payload = {
        session_type: 'login',
        device_fingerprint: 'test-fingerprint-123'
      };

      const headers = {
        'x-device-fingerprint': 'test-fingerprint-123'
      };

      // Make 5 requests (should be allowed)
      for (let i = 0; i < 5; i++) {
        const response = await app.inject({
          method: 'POST',
          url: '/api/v1/qr/generate',
          headers,
          payload
        });
        expect(response.statusCode).toBe(201);
      }

      // 6th request should be rate limited
      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/qr/generate',
        headers,
        payload
      });

      expect(response.statusCode).toBe(400);
      const body = JSON.parse(response.body);
      expect(body.message).toContain('rate limit');
    });
  });

  describe('GET /api/v1/qr/status/:sessionId', () => {
    it('should return QR session status', async () => {
      // First create a QR session
      const generateResponse = await app.inject({
        method: 'POST',
        url: '/api/v1/qr/generate',
        headers: {
          'x-device-fingerprint': 'test-fingerprint-123'
        },
        payload: {
          session_type: 'login',
          device_fingerprint: 'test-fingerprint-123'
        }
      });

      const generateBody = JSON.parse(generateResponse.body);
      const sessionId = generateBody.data.session_id;

      // Check status
      const statusResponse = await app.inject({
        method: 'GET',
        url: `/api/v1/qr/status/${sessionId}`
      });

      expect(statusResponse.statusCode).toBe(200);
      const statusBody = JSON.parse(statusResponse.body);
      expect(statusBody.success).toBe(true);
      expect(statusBody.data.status).toBe('pending');
      expect(statusBody.data.session_type).toBe('login');
    });

    it('should return 404 for non-existent session', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/qr/status/non-existent-session'
      });

      expect(response.statusCode).toBe(404);
    });
  });

  describe('POST /api/v1/qr/scan', () => {
    it('should scan QR code successfully', async () => {
      // First generate a QR code
      const generateResponse = await app.inject({
        method: 'POST',
        url: '/api/v1/qr/generate',
        headers: {
          'x-device-fingerprint': 'test-fingerprint-123'
        },
        payload: {
          session_type: 'login',
          device_fingerprint: 'test-fingerprint-123'
        }
      });

      const generateBody = JSON.parse(generateResponse.body);
      const sessionId = generateBody.data.session_id;

      // Get the QR data from database
      const session = await QRSession.findOne({ session_id: sessionId });
      const qrData = session!.qr_code_data;

      // Scan the QR code
      const scanResponse = await app.inject({
        method: 'POST',
        url: '/api/v1/qr/scan',
        headers: {
          'authorization': `Bearer ${authToken}`,
          'x-device-id': 'mobile-device-123'
        },
        payload: {
          qr_data: qrData
        }
      });

      expect(scanResponse.statusCode).toBe(200);
      const scanBody = JSON.parse(scanResponse.body);
      expect(scanBody.success).toBe(true);
      expect(scanBody.data.session_id).toBe(sessionId);
      expect(scanBody.data.session_type).toBe('login');
      expect(scanBody.data).toHaveProperty('challenge');
      expect(scanBody.data).toHaveProperty('nonce');
    });

    it('should require authentication', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/qr/scan',
        payload: {
          qr_data: 'some-qr-data'
        }
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('POST /api/v1/qr/approve', () => {
    it('should approve QR authentication', async () => {
      // Create a scanned session
      const session = new QRSession({
        session_id: 'test-session-123',
        qr_code_data: '{}',
        session_type: 'login',
        status: 'scanned',
        scanner_phone: '+1234567890',
        initiator_ip: '***********',
        initiator_user_agent: 'Test Browser',
        initiator_fingerprint: 'test-fingerprint',
        security_data: {
          nonce: 'nonce',
          challenge: 'challenge',
          salt: 'salt'
        },
        expires_at: new Date(Date.now() + 60000)
      });
      await session.save();

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/qr/approve',
        headers: {
          'authorization': `Bearer ${authToken}`
        },
        payload: {
          session_id: 'test-session-123',
          device_name: 'Test Device',
          device_type: 'web',
          identity_key: 'identity-key',
          signed_pre_key: 'signed-pre-key',
          pre_key_bundle: 'pre-key-bundle',
          registration_id: 'registration-id',
          device_fingerprint: 'device-fingerprint'
        }
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data.session_approved).toBe(true);
      expect(body.data).toHaveProperty('auth_token');
    });
  });

  describe('POST /api/v1/qr/device/initiate', () => {
    it('should initiate device linking', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/qr/device/initiate',
        headers: {
          'authorization': `Bearer ${authToken}`,
          'x-device-id': 'primary-device-123',
          'x-device-fingerprint': 'new-device-fingerprint'
        },
        payload: {
          device_name: 'New Android Device',
          device_type: 'android',
          device_fingerprint: 'new-device-fingerprint',
          identity_key: 'identity-key',
          signed_pre_key: 'signed-pre-key',
          pre_key_bundle: 'pre-key-bundle',
          registration_id: 'registration-id'
        }
      });

      expect(response.statusCode).toBe(201);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data).toHaveProperty('session_id');
      expect(body.data).toHaveProperty('verification_code');
      expect(body.data).toHaveProperty('expires_at');
      expect(body.data.verification_code).toMatch(/^\d{6}$/);
    });
  });

  describe('GET /api/v1/qr/devices/list', () => {
    it('should list linked devices', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/qr/devices/list',
        headers: {
          'authorization': `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data).toHaveProperty('devices');
      expect(body.data).toHaveProperty('total_devices');
      expect(Array.isArray(body.data.devices)).toBe(true);
    });
  });
});

describe('Deep Link API', () => {
  let app: any;
  let authToken: string;

  beforeAll(async () => {
    app = fastify();
    await app.register(deepLinkRoutes, { prefix: '/api/v1/link' });

    authToken = Auth.generateAccessToken('+1234567890');
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /api/v1/link/generate', () => {
    it('should generate deep link for QR scan', async () => {
      // First create a QR session
      const session = new QRSession({
        session_id: 'test-session-123',
        qr_code_data: '{}',
        session_type: 'login',
        status: 'pending',
        initiator_ip: '***********',
        initiator_user_agent: 'Test Browser',
        initiator_fingerprint: 'test-fingerprint',
        security_data: {
          nonce: 'nonce',
          challenge: 'challenge',
          salt: 'salt'
        },
        expires_at: new Date(Date.now() + 60000)
      });
      await session.save();

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/link/generate',
        headers: {
          'authorization': `Bearer ${authToken}`
        },
        payload: {
          action: 'qr_scan',
          session_id: 'test-session-123',
          metadata: {
            challenge: 'challenge',
            nonce: 'nonce'
          }
        }
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data).toHaveProperty('deep_link');
      expect(body.data).toHaveProperty('ios_url');
      expect(body.data).toHaveProperty('android_url');
      expect(body.data).toHaveProperty('web_fallback');
    });
  });

  describe('GET /api/v1/link/app-store', () => {
    it('should return app store links', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/link/app-store'
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data).toHaveProperty('ios_app_store');
      expect(body.data).toHaveProperty('android_play_store');
    });
  });
});
