import twilio from 'twilio';
import { ExternalServiceError, ConfigError } from '../utils/errors';
import { logger } from '../utils/logger';
import { config as appConfig } from '../config';

const twilioConfig = {
  accountSid: process.env.TWILIO_ACCOUNT_SID || appConfig.twilio.accountSid,
  authToken: process.env.TWILIO_AUTH_TOKEN || appConfig.twilio.authToken,
  fromNumber: process.env.TWILIO_FROM_NUMBER || appConfig.twilio.fromNumber,
  enabled: process.env.TWILIO_ENABLED === 'true' || appConfig.twilio.enabled
};

const isConfigured = !!(twilioConfig.accountSid && twilioConfig.authToken && twilioConfig.fromNumber);
const isEnabled = twilioConfig.enabled && isConfigured;
const client = isEnabled ? twilio(twilioConfig.accountSid, twilioConfig.authToken) : null;

if (isEnabled) {
  logger.info('Twilio SMS service initialized successfully');
  logger.info(`Twilio from number: ${twilioConfig.fromNumber}`);
} else if (!isConfigured) {
  logger.warn('Twilio service not configured - missing credentials');
} else {
  logger.info('Twilio service is disabled');
}

export const sendOtp = async (phoneNumber: string, code: string): Promise<void> => {
  if (!isEnabled) {
    logger.warn(`SMS disabled. Code for ${phoneNumber}: ${code}`);
    throw new ExternalServiceError('SMS service is not enabled or configured');
  }

  if (!client) {
    throw new ConfigError('Twilio client not initialized');
  }

  try {
    const message = `Your QuiickChat verification code is: ${code}. Valid for 10 minutes.`;
    logger.info(`Sending SMS verification code to ${phoneNumber}`);

    const result = await client.messages.create({
      body: message,
      from: twilioConfig.fromNumber,
      to: phoneNumber
    });

    logger.info(`SMS sent successfully. SID: ${result.sid}, Status: ${result.status}`);

    if (result.errorCode) {
      logger.error(`SMS error code: ${result.errorCode}, Message: ${result.errorMessage}`);
      throw new ExternalServiceError(`SMS delivery failed: ${result.errorMessage}`);
    }
  } catch (error: any) {
    logger.error(`Failed to send SMS to ${phoneNumber}:`, {
      error: error.message,
      code: error.code,
      moreInfo: error.moreInfo
    });

    if (error.code === 21211) {
      throw new ExternalServiceError('Invalid phone number format');
    } else if (error.code === 21614) {
      throw new ExternalServiceError('Phone number is not a valid mobile number');
    } else if (error.code === 21408) {
      throw new ExternalServiceError('Permission denied to send SMS to this number');
    }

    throw new ExternalServiceError(`SMS delivery failed: ${error.message}`);
  }
};

export const sendCustomMessage = async (phoneNumber: string, message: string): Promise<void> => {
  if (!isEnabled) {
    logger.warn(`SMS disabled. Would send to ${phoneNumber}: ${message}`);
    throw new ExternalServiceError('SMS service is not enabled or configured');
  }

  if (!client) {
    throw new ConfigError('Twilio client not initialized');
  }

  try {
    logger.info(`Sending custom SMS to ${phoneNumber}`);

    const result = await client.messages.create({
      body: message,
      from: twilioConfig.fromNumber,
      to: phoneNumber
    });

    logger.info(`Custom SMS sent successfully. SID: ${result.sid}, Status: ${result.status}`);
  } catch (error: any) {
    logger.error(`Failed to send custom SMS to ${phoneNumber}:`, error);
    throw new ExternalServiceError(`Failed to send custom SMS: ${error.message}`);
  }
};

export const isServiceEnabled = (): boolean => {
  return isEnabled;
};

export const getServiceStatus = (): { enabled: boolean; configured: boolean; fromNumber?: string } => {
  const result: { enabled: boolean; configured: boolean; fromNumber?: string } = {
    enabled: twilioConfig.enabled,
    configured: isConfigured
  };

  if (isConfigured && twilioConfig.fromNumber) {
    result.fromNumber = twilioConfig.fromNumber;
  }

  return result;
};

export const validatePhoneNumber = (phoneNumber: string): boolean => {
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(phoneNumber);
};

export const testSmsService = async (testPhoneNumber: string): Promise<{ success: boolean; message: string; details?: any }> => {
  try {
    if (!isEnabled) {
      return {
        success: false,
        message: 'SMS service is not enabled or configured',
        details: getServiceStatus()
      };
    }

    if (!validatePhoneNumber(testPhoneNumber)) {
      return {
        success: false,
        message: 'Invalid phone number format. Use format: +1234567890'
      };
    }

    const testCode = '123456';
    await sendOtp(testPhoneNumber, testCode);

    return {
      success: true,
      message: 'Test SMS sent successfully',
      details: {
        phone: testPhoneNumber,
        testCode: testCode
      }
    };
  } catch (error: any) {
    return {
      success: false,
      message: `SMS test failed: ${error.message}`,
      details: {
        error: error.message,
        code: error.code
      }
    };
  }
};
