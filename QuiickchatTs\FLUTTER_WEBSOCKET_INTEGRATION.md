# Flutter WebSocket Status Integration

## Dependencies
Add to your `pubspec.yaml`:
```yaml
dependencies:
  web_socket_channel: ^2.4.0
  provider: ^6.1.1
  shared_preferences: ^2.2.2
```

## 1. WebSocket Service Class

```dart
import 'dart:convert';
import 'dart:async';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;

class StatusWebSocketService {
  WebSocketChannel? _channel;
  StreamController<Map<String, dynamic>> _messageController = StreamController.broadcast();
  Timer? _heartbeatTimer;
  String? _accessToken;
  bool _isConnected = false;
  
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;
  bool get isConnected => _isConnected;
  
  Future<void> connect(String accessToken) async {
    _accessToken = accessToken;
    
    try {
      _channel = WebSocketChannel.connect(
        Uri.parse('ws://localhost:3000/status-updates'),
      );
      
      // Listen to messages
      _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
      );
      
      // Authenticate
      _sendMessage({
        'type': 'auth',
        'token': accessToken,
      });
      
      // Start heartbeat
      _startHeartbeat();
      
    } catch (e) {
      print('WebSocket connection error: $e');
      _handleError(e);
    }
  }
  
  void _handleMessage(dynamic message) {
    try {
      final data = jsonDecode(message);
      
      switch (data['type']) {
        case 'auth_success':
          _isConnected = true;
          _requestInitialData();
          break;
          
        case 'error':
          print('WebSocket error: ${data['message']}');
          break;
          
        default:
          _messageController.add(data);
      }
    } catch (e) {
      print('Error parsing WebSocket message: $e');
    }
  }
  
  void _handleError(dynamic error) {
    print('WebSocket error: $error');
    _isConnected = false;
    _scheduleReconnection();
  }
  
  void _handleDisconnection() {
    print('WebSocket disconnected');
    _isConnected = false;
    _scheduleReconnection();
  }
  
  void _scheduleReconnection() {
    Timer(Duration(seconds: 5), () {
      if (!_isConnected && _accessToken != null) {
        connect(_accessToken!);
      }
    });
  }
  
  void _startHeartbeat() {
    _heartbeatTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      if (_isConnected) {
        _sendMessage({'type': 'ping'});
      }
    });
  }
  
  void _requestInitialData() {
    _sendMessage({'type': 'request_status_updates'});
    _sendMessage({'type': 'request_contact_statuses'});
  }
  
  void _sendMessage(Map<String, dynamic> message) {
    if (_channel != null && _isConnected) {
      _channel!.sink.add(jsonEncode(message));
    }
  }
  
  void subscribeToContact(String contactPhone) {
    _sendMessage({
      'type': 'subscribe',
      'contact_phone': contactPhone,
    });
  }
  
  void markStatusViewed(String statusId, String ownerPhone) {
    _sendMessage({
      'type': 'mark_status_viewed',
      'status_id': statusId,
      'owner_phone': ownerPhone,
    });
  }
  
  void trackStatusInteraction(String statusId, String interactionType, String ownerPhone) {
    _sendMessage({
      'type': 'status_interaction',
      'status_id': statusId,
      'interaction_type': interactionType,
      'owner_phone': ownerPhone,
    });
  }
  
  void dispose() {
    _heartbeatTimer?.cancel();
    _channel?.sink.close(status.goingAway);
    _messageController.close();
  }
}
```

## 2. Status Model Classes

```dart
class StatusUpdate {
  final String id;
  final String userPhone;
  final String contentType;
  final String? content;
  final String? contentUrl;
  final String? caption;
  final String? backgroundColor;
  final String? textColor;
  final String? fontStyle;
  final String privacySetting;
  final int viewCount;
  final bool isActive;
  final DateTime expiresAt;
  final DateTime createdAt;
  final double priority;
  final bool isRecent;
  
  StatusUpdate({
    required this.id,
    required this.userPhone,
    required this.contentType,
    this.content,
    this.contentUrl,
    this.caption,
    this.backgroundColor,
    this.textColor,
    this.fontStyle,
    required this.privacySetting,
    required this.viewCount,
    required this.isActive,
    required this.expiresAt,
    required this.createdAt,
    required this.priority,
    required this.isRecent,
  });
  
  factory StatusUpdate.fromJson(Map<String, dynamic> json) {
    return StatusUpdate(
      id: json['id'],
      userPhone: json['user_phone'],
      contentType: json['content_type'],
      content: json['content'],
      contentUrl: json['content_url'],
      caption: json['caption'],
      backgroundColor: json['background_color'],
      textColor: json['text_color'],
      fontStyle: json['font_style'],
      privacySetting: json['privacy_setting'],
      viewCount: json['view_count'],
      isActive: json['is_active'],
      expiresAt: DateTime.parse(json['expires_at']),
      createdAt: DateTime.parse(json['created_at']),
      priority: (json['priority'] ?? 0).toDouble(),
      isRecent: json['is_recent'] ?? false,
    );
  }
}

class ContactStatusSummary {
  final String userPhone;
  final String? username;
  final String? profilePicture;
  final int totalStatuses;
  final int unviewedStatuses;
  final DateTime latestStatusTime;
  final String latestStatusType;
  final bool hasRecentActivity;
  
  ContactStatusSummary({
    required this.userPhone,
    this.username,
    this.profilePicture,
    required this.totalStatuses,
    required this.unviewedStatuses,
    required this.latestStatusTime,
    required this.latestStatusType,
    required this.hasRecentActivity,
  });
  
  factory ContactStatusSummary.fromJson(Map<String, dynamic> json) {
    return ContactStatusSummary(
      userPhone: json['user_phone'],
      username: json['username'],
      profilePicture: json['profile_picture'],
      totalStatuses: json['total_statuses'],
      unviewedStatuses: json['unviewed_statuses'],
      latestStatusTime: DateTime.parse(json['latest_status_time']),
      latestStatusType: json['latest_status_type'],
      hasRecentActivity: json['has_recent_activity'],
    );
  }
}
```

## 3. Status Provider (State Management)

```dart
import 'package:flutter/foundation.dart';

class StatusProvider extends ChangeNotifier {
  final StatusWebSocketService _webSocketService = StatusWebSocketService();
  
  List<StatusUpdate> _statuses = [];
  List<ContactStatusSummary> _contactSummaries = [];
  bool _isConnected = false;
  
  List<StatusUpdate> get statuses => _statuses;
  List<ContactStatusSummary> get contactSummaries => _contactSummaries;
  bool get isConnected => _isConnected;
  
  StatusProvider() {
    _webSocketService.messageStream.listen(_handleWebSocketMessage);
  }
  
  Future<void> connect(String accessToken) async {
    await _webSocketService.connect(accessToken);
  }
  
  void _handleWebSocketMessage(Map<String, dynamic> data) {
    switch (data['type']) {
      case 'status_update':
        _handleNewStatusUpdate(data);
        break;
        
      case 'status_updates_batch':
        _handleBatchStatusUpdates(data['statuses']);
        break;
        
      case 'contact_statuses':
        _handleContactStatusSummaries(data['summaries']);
        break;
        
      case 'status_viewed':
        _handleStatusViewed(data);
        break;
        
      case 'status_deleted':
        _handleStatusDeleted(data);
        break;
    }
    
    _isConnected = _webSocketService.isConnected;
    notifyListeners();
  }
  
  void _handleNewStatusUpdate(Map<String, dynamic> data) {
    final statusData = data['data'];
    final newStatus = StatusUpdate.fromJson(statusData);
    
    // Find optimal insert position based on priority
    int insertIndex = _findOptimalInsertPosition(newStatus);
    _statuses.insert(insertIndex, newStatus);
    
    // Show notification for high-priority statuses
    if (newStatus.priority > 5 && newStatus.isRecent) {
      _showStatusNotification(newStatus);
    }
  }
  
  void _handleBatchStatusUpdates(List<dynamic> statusList) {
    _statuses = statusList
        .map((status) => StatusUpdate.fromJson(status))
        .toList();
    
    // Sort by priority and recency
    _statuses.sort((a, b) {
      if (a.priority != b.priority) {
        return b.priority.compareTo(a.priority);
      }
      return b.createdAt.compareTo(a.createdAt);
    });
  }
  
  void _handleContactStatusSummaries(List<dynamic> summaries) {
    _contactSummaries = summaries
        .map((summary) => ContactStatusSummary.fromJson(summary))
        .toList();
  }
  
  void _handleStatusViewed(Map<String, dynamic> data) {
    final statusId = data['status_id'];
    final viewerPhone = data['viewer_phone'];
    
    // Update view count for the status
    final statusIndex = _statuses.indexWhere((s) => s.id == statusId);
    if (statusIndex != -1) {
      // Create updated status with incremented view count
      // Note: You might want to implement a proper update mechanism
      notifyListeners();
    }
  }
  
  void _handleStatusDeleted(Map<String, dynamic> data) {
    final statusId = data['status_id'];
    _statuses.removeWhere((status) => status.id == statusId);
  }
  
  int _findOptimalInsertPosition(StatusUpdate newStatus) {
    for (int i = 0; i < _statuses.length; i++) {
      final currentStatus = _statuses[i];
      
      // Priority-based sorting
      if (newStatus.priority > currentStatus.priority) {
        return i;
      }
      
      // If same priority, sort by recency
      if (newStatus.priority == currentStatus.priority) {
        if (newStatus.createdAt.isAfter(currentStatus.createdAt)) {
          return i;
        }
      }
    }
    
    return _statuses.length; // Insert at end
  }
  
  void _showStatusNotification(StatusUpdate status) {
    // Implement local notification logic here
    print('New high-priority status from ${status.userPhone}');
  }
  
  void viewStatus(String statusId, String ownerPhone) {
    _webSocketService.markStatusViewed(statusId, ownerPhone);
    _webSocketService.trackStatusInteraction(statusId, 'view', ownerPhone);
  }
  
  void likeStatus(String statusId, String ownerPhone) {
    _webSocketService.trackStatusInteraction(statusId, 'like', ownerPhone);
  }
  
  void shareStatus(String statusId, String ownerPhone) {
    _webSocketService.trackStatusInteraction(statusId, 'share', ownerPhone);
  }
  
  void subscribeToContact(String contactPhone) {
    _webSocketService.subscribeToContact(contactPhone);
  }
  
  @override
  void dispose() {
    _webSocketService.dispose();
    super.dispose();
  }
}
```

## 4. UI Implementation

```dart
class StatusListWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<StatusProvider>(
      builder: (context, statusProvider, child) {
        if (!statusProvider.isConnected) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Connecting to status updates...'),
              ],
            ),
          );
        }
        
        return ListView.builder(
          itemCount: statusProvider.statuses.length,
          itemBuilder: (context, index) {
            final status = statusProvider.statuses[index];
            return StatusCard(
              status: status,
              onView: () => statusProvider.viewStatus(status.id, status.userPhone),
              onLike: () => statusProvider.likeStatus(status.id, status.userPhone),
              onShare: () => statusProvider.shareStatus(status.id, status.userPhone),
            );
          },
        );
      },
    );
  }
}

class StatusCard extends StatelessWidget {
  final StatusUpdate status;
  final VoidCallback onView;
  final VoidCallback onLike;
  final VoidCallback onShare;
  
  const StatusCard({
    Key? key,
    required this.status,
    required this.onView,
    required this.onLike,
    required this.onShare,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(8),
      child: InkWell(
        onTap: onView,
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status header
              Row(
                children: [
                  CircleAvatar(
                    child: Text(status.userPhone.substring(0, 2)),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          status.userPhone,
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          _formatTime(status.createdAt),
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                  if (status.isRecent)
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'NEW',
                        style: TextStyle(color: Colors.white, fontSize: 10),
                      ),
                    ),
                ],
              ),
              
              SizedBox(height: 12),
              
              // Status content
              if (status.contentType == 'text')
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: status.backgroundColor != null 
                        ? Color(int.parse(status.backgroundColor!.replaceFirst('#', '0xFF')))
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    status.content ?? status.caption ?? '',
                    style: TextStyle(
                      color: status.textColor != null
                          ? Color(int.parse(status.textColor!.replaceFirst('#', '0xFF')))
                          : Colors.black,
                      fontWeight: status.fontStyle == 'bold' ? FontWeight.bold : FontWeight.normal,
                      fontStyle: status.fontStyle == 'italic' ? FontStyle.italic : FontStyle.normal,
                    ),
                  ),
                )
              else if (status.contentType == 'image')
                Image.network(
                  status.contentUrl!,
                  fit: BoxFit.cover,
                  height: 200,
                  width: double.infinity,
                )
              else if (status.contentType == 'video')
                Container(
                  height: 200,
                  width: double.infinity,
                  color: Colors.black,
                  child: Center(
                    child: Icon(Icons.play_circle_fill, size: 64, color: Colors.white),
                  ),
                ),
              
              if (status.caption != null && status.contentType != 'text')
                Padding(
                  padding: EdgeInsets.only(top: 8),
                  child: Text(status.caption!),
                ),
              
              SizedBox(height: 12),
              
              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  TextButton.icon(
                    onPressed: onLike,
                    icon: Icon(Icons.favorite_border),
                    label: Text('Like'),
                  ),
                  TextButton.icon(
                    onPressed: onShare,
                    icon: Icon(Icons.share),
                    label: Text('Share'),
                  ),
                  Text(
                    '${status.viewCount} views',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
```

## 5. Main App Integration

```dart
void main() {
  runApp(
    ChangeNotifierProvider(
      create: (context) => StatusProvider(),
      child: MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'QuickChat Status',
      home: StatusScreen(),
    );
  }
}

class StatusScreen extends StatefulWidget {
  @override
  _StatusScreenState createState() => _StatusScreenState();
}

class _StatusScreenState extends State<StatusScreen> {
  @override
  void initState() {
    super.initState();
    _connectToWebSocket();
  }
  
  void _connectToWebSocket() async {
    // Get access token from secure storage
    final accessToken = await getAccessToken(); // Your method
    
    if (accessToken != null) {
      Provider.of<StatusProvider>(context, listen: false).connect(accessToken);
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Status Updates'),
        actions: [
          Consumer<StatusProvider>(
            builder: (context, statusProvider, child) {
              return Icon(
                statusProvider.isConnected ? Icons.wifi : Icons.wifi_off,
                color: statusProvider.isConnected ? Colors.green : Colors.red,
              );
            },
          ),
        ],
      ),
      body: StatusListWidget(),
    );
  }
}
```

This Flutter integration provides:
- Real-time WebSocket connection with auto-reconnection
- Intelligent status sorting based on priority and user interactions
- Proper state management with Provider
- Offline handling and connection status indicators
- Interactive status cards with view/like/share tracking
- Automatic UI updates when new statuses arrive
