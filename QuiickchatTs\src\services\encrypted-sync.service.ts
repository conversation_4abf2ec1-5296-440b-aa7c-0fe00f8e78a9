import { User, IDeviceRecord } from '../models/user.model';
import { logger } from '../utils/logger';
import { ValidationError, NotFoundError } from '../utils/errors';
import { CryptoService } from './crypto.service';
import { wsManager } from './websocket.service';
import { v4 as uuidv4 } from 'uuid';

interface SyncMessage {
  messageId: string;
  messageType: 'contact_sync' | 'message_sync' | 'status_sync' | 'settings_sync' | 'key_rotation';
  fromDeviceId: string;
  toDeviceId: string;
  timestamp: Date;
  payload: any;
}

interface EncryptedSyncMessage {
  messageId: string;
  messageType: string;
  fromDeviceId: string;
  toDeviceId: string;
  encryptedPayload: string;
  authTag: string;
  timestamp: Date;
}

interface DeviceSession {
  deviceId: string;
  rootKey: Uint8Array;
  sendingChainKey: Uint8Array;
  receivingChainKey: Uint8Array;
  sendingMessageNumber: number;
  receivingMessageNumber: number;
  skippedMessages: Map<number, Uint8Array>;
}

export class EncryptedSyncService {
  private static deviceSessions = new Map<string, DeviceSession>();
  private static readonly MAX_SKIPPED_MESSAGES = 1000;
  private static readonly MESSAGE_RETENTION_HOURS = 24;

  static async initializeDeviceSession(
    userPhone: string,
    deviceId1: string,
    deviceId2: string
  ): Promise<{ success: boolean; sessionId: string }> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const device1 = user.devices.find(d => d.device_id === deviceId1);
    const device2 = user.devices.find(d => d.device_id === deviceId2);

    if (!device1 || !device2) {
      throw new NotFoundError('One or both devices not found');
    }

    const sessionId = `${deviceId1}-${deviceId2}`;
    const reverseSessionId = `${deviceId2}-${deviceId1}`;

    if (this.deviceSessions.has(sessionId) || this.deviceSessions.has(reverseSessionId)) {
      logger.info(`Device session already exists: ${sessionId}`);
      return { success: true, sessionId };
    }

    const sharedSecret = this.deriveSharedSecret(device1, device2);
    const rootKey = CryptoService.deriveRootKey(sharedSecret);
    
    const initialChainKey = CryptoService.generateSecureRandom(32);

    const session1: DeviceSession = {
      deviceId: deviceId1,
      rootKey,
      sendingChainKey: initialChainKey,
      receivingChainKey: initialChainKey,
      sendingMessageNumber: 0,
      receivingMessageNumber: 0,
      skippedMessages: new Map()
    };

    const session2: DeviceSession = {
      deviceId: deviceId2,
      rootKey,
      sendingChainKey: initialChainKey,
      receivingChainKey: initialChainKey,
      sendingMessageNumber: 0,
      receivingMessageNumber: 0,
      skippedMessages: new Map()
    };

    this.deviceSessions.set(sessionId, session1);
    this.deviceSessions.set(reverseSessionId, session2);

    logger.info(`Device session initialized between ${deviceId1} and ${deviceId2}`);
    return { success: true, sessionId };
  }

  static async sendSyncMessage(
    userPhone: string,
    fromDeviceId: string,
    toDeviceId: string,
    messageType: string,
    payload: any
  ): Promise<{ success: boolean; messageId: string }> {
    const sessionId = `${fromDeviceId}-${toDeviceId}`;
    const session = this.deviceSessions.get(sessionId);

    if (!session) {
      await this.initializeDeviceSession(userPhone, fromDeviceId, toDeviceId);
      const newSession = this.deviceSessions.get(sessionId);
      if (!newSession) {
        throw new ValidationError('Failed to initialize device session');
      }
    }

    const messageId = uuidv4();
    const syncMessage: SyncMessage = {
      messageId,
      messageType: messageType as any,
      fromDeviceId,
      toDeviceId,
      timestamp: new Date(),
      payload
    };

    const encryptedMessage = await this.encryptSyncMessage(syncMessage, sessionId);
    
    await this.deliverSyncMessage(userPhone, encryptedMessage);

    logger.info(`Sync message sent from ${fromDeviceId} to ${toDeviceId}: ${messageType}`);
    return { success: true, messageId };
  }

  static async receiveSyncMessage(
    userPhone: string,
    encryptedMessage: EncryptedSyncMessage
  ): Promise<{ success: boolean; message?: SyncMessage }> {
    const sessionId = `${encryptedMessage.fromDeviceId}-${encryptedMessage.toDeviceId}`;
    const session = this.deviceSessions.get(sessionId);

    if (!session) {
      throw new ValidationError('Device session not found');
    }

    try {
      const decryptedMessage = await this.decryptSyncMessage(encryptedMessage, sessionId);
      
      await this.processSyncMessage(userPhone, decryptedMessage);

      logger.info(`Sync message received: ${decryptedMessage.messageType}`);
      return { success: true, message: decryptedMessage };
    } catch (error) {
      logger.error('Failed to decrypt sync message:', error);
      return { success: false };
    }
  }

  static async syncContacts(userPhone: string, fromDeviceId: string, contacts: any[]): Promise<{ success: boolean }> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const otherDevices = user.devices.filter(d => d.device_id !== fromDeviceId && d.is_active);

    for (const device of otherDevices) {
      await this.sendSyncMessage(userPhone, fromDeviceId, device.device_id, 'contact_sync', {
        contacts,
        syncType: 'full',
        timestamp: new Date()
      });
    }

    logger.info(`Contacts synced from ${fromDeviceId} to ${otherDevices.length} devices`);
    return { success: true };
  }

  static async syncMessages(
    userPhone: string,
    fromDeviceId: string,
    messages: any[],
    contactId: string
  ): Promise<{ success: boolean }> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const otherDevices = user.devices.filter(d => d.device_id !== fromDeviceId && d.is_active);

    for (const device of otherDevices) {
      await this.sendSyncMessage(userPhone, fromDeviceId, device.device_id, 'message_sync', {
        messages,
        contactId,
        syncType: 'incremental',
        timestamp: new Date()
      });
    }

    logger.info(`Messages synced from ${fromDeviceId} to ${otherDevices.length} devices for contact ${contactId}`);
    return { success: true };
  }

  static async syncSettings(
    userPhone: string,
    fromDeviceId: string,
    settings: any
  ): Promise<{ success: boolean }> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const otherDevices = user.devices.filter(d => d.device_id !== fromDeviceId && d.is_active);

    for (const device of otherDevices) {
      await this.sendSyncMessage(userPhone, fromDeviceId, device.device_id, 'settings_sync', {
        settings,
        timestamp: new Date()
      });
    }

    logger.info(`Settings synced from ${fromDeviceId} to ${otherDevices.length} devices`);
    return { success: true };
  }

  static async rotateKeys(userPhone: string, deviceId: string): Promise<{ success: boolean }> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const device = user.devices.find(d => d.device_id === deviceId);
    if (!device) {
      throw new NotFoundError('Device not found');
    }

    const newIdentityKeyPair = CryptoService.generateIdentityKeyPair();
    const newSignedPreKey = CryptoService.generatePreKey(Date.now());

    device.identity_key = Buffer.from(newIdentityKeyPair.publicKey).toString('base64');
    device.signed_pre_key = Buffer.from(newSignedPreKey.keyPair.publicKey).toString('base64');
    device.last_key_update = Date.now();

    await user.save();

    const otherDevices = user.devices.filter(d => d.device_id !== deviceId && d.is_active);

    for (const otherDevice of otherDevices) {
      await this.sendSyncMessage(userPhone, deviceId, otherDevice.device_id, 'key_rotation', {
        newIdentityKey: device.identity_key,
        newSignedPreKey: device.signed_pre_key,
        timestamp: new Date()
      });

      this.deviceSessions.delete(`${deviceId}-${otherDevice.device_id}`);
      this.deviceSessions.delete(`${otherDevice.device_id}-${deviceId}`);
    }

    logger.info(`Keys rotated for device ${deviceId}`);
    return { success: true };
  }

  private static deriveSharedSecret(device1: IDeviceRecord, device2: IDeviceRecord): Uint8Array {
    const key1 = Buffer.from(device1.identity_key, 'base64');
    const key2 = Buffer.from(device2.identity_key, 'base64');
    
    const combined = Buffer.concat([key1, key2].sort(Buffer.compare));
    const hash = CryptoService.generateSecureRandom(32);
    
    return hash;
  }

  private static async encryptSyncMessage(
    message: SyncMessage,
    sessionId: string
  ): Promise<EncryptedSyncMessage> {
    const session = this.deviceSessions.get(sessionId);
    if (!session) {
      throw new ValidationError('Device session not found');
    }

    const messageKeyResult = CryptoService.deriveMessageKey(session.sendingChainKey);
    session.sendingChainKey = messageKeyResult.chainKey;
    session.sendingMessageNumber++;

    const plaintext = Buffer.from(JSON.stringify(message.payload), 'utf8');
    const associatedData = Buffer.from(`${message.messageType}:${message.messageId}`, 'utf8');

    const encryptionResult = CryptoService.encryptMessage(
      messageKeyResult.messageKey,
      plaintext,
      associatedData
    );

    return {
      messageId: message.messageId,
      messageType: message.messageType,
      fromDeviceId: message.fromDeviceId,
      toDeviceId: message.toDeviceId,
      encryptedPayload: Buffer.from(encryptionResult.ciphertext).toString('base64'),
      authTag: Buffer.from(encryptionResult.tag).toString('base64'),
      timestamp: message.timestamp
    };
  }

  private static async decryptSyncMessage(
    encryptedMessage: EncryptedSyncMessage,
    sessionId: string
  ): Promise<SyncMessage> {
    const session = this.deviceSessions.get(sessionId);
    if (!session) {
      throw new ValidationError('Device session not found');
    }

    const messageKeyResult = CryptoService.deriveMessageKey(session.receivingChainKey);
    session.receivingChainKey = messageKeyResult.chainKey;
    session.receivingMessageNumber++;

    const ciphertext = Buffer.from(encryptedMessage.encryptedPayload, 'base64');
    const authTag = Buffer.from(encryptedMessage.authTag, 'base64');
    const associatedData = Buffer.from(`${encryptedMessage.messageType}:${encryptedMessage.messageId}`, 'utf8');

    const plaintext = CryptoService.decryptMessage(
      messageKeyResult.messageKey,
      ciphertext,
      authTag,
      associatedData
    );

    const payload = JSON.parse(Buffer.from(plaintext).toString('utf8'));

    return {
      messageId: encryptedMessage.messageId,
      messageType: encryptedMessage.messageType as any,
      fromDeviceId: encryptedMessage.fromDeviceId,
      toDeviceId: encryptedMessage.toDeviceId,
      timestamp: encryptedMessage.timestamp,
      payload
    };
  }

  private static async deliverSyncMessage(
    userPhone: string,
    encryptedMessage: EncryptedSyncMessage
  ): Promise<void> {
    const messageData = {
      type: 'device_sync',
      data: encryptedMessage
    };

    wsManager.sendToUserDevices(userPhone, JSON.stringify(messageData));
  }

  private static async processSyncMessage(userPhone: string, message: SyncMessage): Promise<void> {
    switch (message.messageType) {
      case 'contact_sync':
        await this.processContactSync(userPhone, message.payload);
        break;
      case 'message_sync':
        await this.processMessageSync(userPhone, message.payload);
        break;
      case 'settings_sync':
        await this.processSettingsSync(userPhone, message.payload);
        break;
      case 'key_rotation':
        await this.processKeyRotation(userPhone, message.fromDeviceId, message.payload);
        break;
      default:
        logger.warn(`Unknown sync message type: ${message.messageType}`);
    }
  }

  private static async processContactSync(userPhone: string, payload: any): Promise<void> {
    logger.info(`Processing contact sync for user ${userPhone}`);
  }

  private static async processMessageSync(userPhone: string, payload: any): Promise<void> {
    logger.info(`Processing message sync for user ${userPhone}`);
  }

  private static async processSettingsSync(userPhone: string, payload: any): Promise<void> {
    logger.info(`Processing settings sync for user ${userPhone}`);
  }

  private static async processKeyRotation(userPhone: string, deviceId: string, payload: any): Promise<void> {
    logger.info(`Processing key rotation for device ${deviceId}`);
  }
}
