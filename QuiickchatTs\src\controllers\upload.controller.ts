import { FastifyRequest, FastifyReply } from 'fastify';
import { uploadBuffer } from '../services/cloudinary.service';
import { ValidationError } from '../utils/errors';
import { logger } from '../utils/logger';
import { AuthenticatedRequest } from '../types';

export const uploadChatMedia = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const data = await request.file();
    
    if (!data) {
      return reply.status(400).send({
        success: false,
        message: 'No file provided'
      });
    }

    const allowedMimeTypes = [
      'image/jpeg',
      'image/png', 
      'image/gif',
      'image/webp',
      'video/mp4',
      'video/mpeg',
      'video/quicktime',
      'video/webm',
      'audio/mpeg',
      'audio/wav',
      'audio/ogg',
      'application/pdf',
      'text/plain'
    ];

    if (!allowedMimeTypes.includes(data.mimetype)) {
      return reply.status(400).send({
        success: false,
        message: 'File type not supported. Allowed: images, videos, audio, PDF, text files'
      });
    }

    const maxSizes = {
      image: 10 * 1024 * 1024,    // 10MB for images
      video: 100 * 1024 * 1024,   // 100MB for videos  
      audio: 50 * 1024 * 1024,    // 50MB for audio
      document: 25 * 1024 * 1024  // 25MB for documents
    };

    let maxSize = maxSizes.document;
    if (data.mimetype.startsWith('image/')) {
      maxSize = maxSizes.image;
    } else if (data.mimetype.startsWith('video/')) {
      maxSize = maxSizes.video;
    } else if (data.mimetype.startsWith('audio/')) {
      maxSize = maxSizes.audio;
    }

    const buffer = await data.toBuffer();
    
    if (buffer.length > maxSize) {
      const sizeMB = Math.round(maxSize / (1024 * 1024));
      return reply.status(400).send({
        success: false,
        message: `File too large. Maximum size: ${sizeMB}MB`
      });
    }

    const folder = `chat-media/${request.user.phone}`;
    const resourceType = data.mimetype.startsWith('video/') ? 'video' :
                        data.mimetype.startsWith('audio/') ? 'video' : 'auto';

    const uploadResult = await uploadBuffer(buffer, {
      folder,
      resource_type: resourceType
    });

    const mediaType = data.mimetype.startsWith('image/') ? 'image' : 
                     data.mimetype.startsWith('video/') ? 'video' :
                     data.mimetype.startsWith('audio/') ? 'audio' : 'document';

    logger.info(`Chat media uploaded for user ${request.user.phone}: ${uploadResult.secure_url}`);

    return reply.status(200).send({
      success: true,
      message: 'Media uploaded successfully',
      data: {
        url: uploadResult.secure_url,
        public_id: uploadResult.public_id,
        media_type: mediaType,
        file_size: buffer.length,
        mime_type: data.mimetype,
        filename: data.filename || 'untitled',
        uploaded_at: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Error uploading chat media:', error);
    
    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to upload media'
    });
  }
};

export const uploadMultipleChatMedia = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const files = request.files();
    const uploadResults = [];
    const maxFiles = 10;
    let fileCount = 0;

    for await (const file of files) {
      fileCount++;
      
      if (fileCount > maxFiles) {
        return reply.status(400).send({
          success: false,
          message: `Too many files. Maximum ${maxFiles} files allowed per upload`
        });
      }

      const allowedMimeTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'video/mp4', 'video/mpeg', 'video/quicktime', 'video/webm',
        'audio/mpeg', 'audio/wav', 'audio/ogg',
        'application/pdf', 'text/plain'
      ];

      if (!allowedMimeTypes.includes(file.mimetype)) {
        continue;
      }

      const buffer = await file.toBuffer();
      
      let maxSize = 25 * 1024 * 1024;
      if (file.mimetype.startsWith('image/')) maxSize = 10 * 1024 * 1024;
      else if (file.mimetype.startsWith('video/')) maxSize = 100 * 1024 * 1024;
      else if (file.mimetype.startsWith('audio/')) maxSize = 50 * 1024 * 1024;

      if (buffer.length > maxSize) {
        continue;
      }

      try {
        const folder = `chat-media/${request.user.phone}`;
        const resourceType = file.mimetype.startsWith('video/') ? 'video' :
                            file.mimetype.startsWith('audio/') ? 'video' : 'auto';

        const uploadResult = await uploadBuffer(buffer, {
          folder,
          resource_type: resourceType
        });

        const mediaType = file.mimetype.startsWith('image/') ? 'image' : 
                         file.mimetype.startsWith('video/') ? 'video' :
                         file.mimetype.startsWith('audio/') ? 'audio' : 'document';

        uploadResults.push({
          url: uploadResult.secure_url,
          public_id: uploadResult.public_id,
          media_type: mediaType,
          file_size: buffer.length,
          mime_type: file.mimetype,
          filename: file.filename || 'untitled',
          uploaded_at: new Date().toISOString()
        });
      } catch (uploadError) {
        logger.error(`Failed to upload file ${file.filename}:`, uploadError);
      }
    }

    if (uploadResults.length === 0) {
      return reply.status(400).send({
        success: false,
        message: 'No valid files were uploaded'
      });
    }

    logger.info(`Multiple chat media uploaded for user ${request.user.phone}: ${uploadResults.length} files`);

    return reply.status(200).send({
      success: true,
      message: `${uploadResults.length} files uploaded successfully`,
      data: {
        files: uploadResults,
        total_uploaded: uploadResults.length
      }
    });

  } catch (error: any) {
    logger.error('Error uploading multiple chat media:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to upload media files'
    });
  }
};

export const deleteUploadedMedia = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const { public_id } = request.params as { public_id: string };
    
    if (!public_id) {
      return reply.status(400).send({
        success: false,
        message: 'Public ID is required'
      });
    }

    const cloudinary = require('cloudinary').v2;
    const result = await cloudinary.uploader.destroy(public_id);

    if (result.result === 'ok') {
      logger.info(`Media deleted for user ${request.user.phone}: ${public_id}`);
      
      return reply.status(200).send({
        success: true,
        message: 'Media deleted successfully'
      });
    } else {
      return reply.status(404).send({
        success: false,
        message: 'Media not found or already deleted'
      });
    }

  } catch (error: any) {
    logger.error('Error deleting media:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to delete media'
    });
  }
};
