import { FastifyRequest, FastifyReply } from 'fastify';
import * as zengoService from '../services/zengo.service';
import { ValidationError } from '../utils/errors';
import { logger } from '../utils/logger';
import {
  AuthenticatedRequest,
  ZengoModifyUserInfo,
  ZengoFriendInfo,
  ZengoFriendRequestInfo,
  ZengoQueryFriendListResponse,
  ZengoFriendAliasUpdate,
  ZengoFriendAttribute,
  ZengoQueryBlocklistResponse,
  ZengoCheckBlockshipResponse
} from '../types';

export const getServiceStatus = async (_request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const status = zengoService.getServiceStatus();
    
    return reply.status(200).send({
      success: true,
      message: 'Z service status retrieved',
      data: status
    });
  } catch (error: any) {
    logger.error('Error getting Zengo service status:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to get service status'
    });
  }
};

export const getUserOnlineStatus = async (
  request: FastifyRequest<{ Querystring: { user_ids: string } }>, 
  reply: FastifyReply
): Promise<any> => {
  const { user_ids } = request.query;

  if (!user_ids) {
    return reply.status(400).send({
      success: false,
      message: 'User IDs are required'
    });
  }

  try {
    const userIdArray = user_ids.split(',').map(id => id.trim()).filter(id => id.length > 0);
    
    if (userIdArray.length === 0) {
      return reply.status(400).send({
        success: false,
        message: 'At least one valid user ID is required'
      });
    }

    const response = await zengoService.getUserOnlineStatus(userIdArray);
    
    return reply.status(200).send({
      success: true,
      message: 'User online status retrieved',
      data: response
    });
  } catch (error: any) {
    logger.error('Error getting user online status:', error);
    
    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }
    
    return reply.status(500).send({
      success: false,
      message: 'Failed to get user online status'
    });
  }
};

export const getUserInfo = async (
  request: FastifyRequest<{ Querystring: { user_ids: string } }>, 
  reply: FastifyReply
): Promise<any> => {
  const { user_ids } = request.query;

  if (!user_ids) {
    return reply.status(400).send({
      success: false,
      message: 'User IDs are required'
    });
  }

  try {
    const userIdArray = user_ids.split(',').map(id => id.trim()).filter(id => id.length > 0);
    
    if (userIdArray.length === 0) {
      return reply.status(400).send({
        success: false,
        message: 'At least one valid user ID is required'
      });
    }

    const response = await zengoService.getUserInfo(userIdArray);
    
    return reply.status(200).send({
      success: true,
      message: 'User info retrieved',
      data: response
    });
  } catch (error: any) {
    logger.error('Error getting user info:', error);
    
    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }
    
    return reply.status(500).send({
      success: false,
      message: 'Failed to get user info'
    });
  }
};

export const registerCurrentUser = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'Authentication required'
    });
  }

  try {
    const response = await zengoService.registerUser(
      request.user.phone,
      undefined,
      undefined
    );
    
    return reply.status(200).send({
      success: true,
      message: 'User registered with Mother chat',
      data: response
    });
  } catch (error: any) {
    logger.error('Error registering current user with Mother chat:', error);
    
    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }
    
    return reply.status(500).send({
      success: false,
      message: 'Error registering current user with Mother chat'
    });
  }
};

export const modifyUserInfo = async (
  request: FastifyRequest<{ Body: { users: ZengoModifyUserInfo[] } }>,
  reply: FastifyReply
): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { users } = request.body;

  if (!users || !Array.isArray(users)) {
    return reply.status(400).send({
      success: false,
      message: 'Users array is required'
    });
  }

  try {
    const response = await zengoService.modifyUserInfo(users);

    return reply.status(200).send({
      success: true,
      message: 'User info modified successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error modifying user info:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to modify user info'
    });
  }
};

export const addFriends = async (
  request: FastifyRequest<{ Body: { fromUserId: string; friends: ZengoFriendInfo[] } }>,
  reply: FastifyReply
): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { fromUserId, friends } = request.body;

  if (!fromUserId || !friends) {
    return reply.status(400).send({
      success: false,
      message: 'From user ID and friends array are required'
    });
  }

  try {
    const response = await zengoService.addFriends(fromUserId, friends);

    return reply.status(200).send({
      success: true,
      message: 'Friends added successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error adding friends:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to add friends'
    });
  }
};

export const sendFriendRequests = async (
  request: FastifyRequest<{ Body: { fromUserId: string; requests: ZengoFriendRequestInfo[] } }>,
  reply: FastifyReply
): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { fromUserId, requests } = request.body;

  if (!fromUserId || !requests) {
    return reply.status(400).send({
      success: false,
      message: 'From user ID and requests array are required'
    });
  }

  try {
    const response = await zengoService.sendFriendRequests(fromUserId, requests);

    return reply.status(200).send({
      success: true,
      message: 'Friend requests sent successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error sending friend requests:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to send friend requests'
    });
  }
};

export const deleteFriends = async (
  request: FastifyRequest<{ Body: { fromUserId: string; userIds: string[]; deleteType: 0 | 1 } }>,
  reply: FastifyReply
): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { fromUserId, userIds, deleteType } = request.body;

  if (!fromUserId || !userIds || deleteType === undefined) {
    return reply.status(400).send({
      success: false,
      message: 'From user ID, user IDs array, and delete type are required'
    });
  }

  if (![0, 1].includes(deleteType)) {
    return reply.status(400).send({
      success: false,
      message: 'Delete type must be 0 (two-way) or 1 (one-way)'
    });
  }

  try {
    const response = await zengoService.deleteFriends(fromUserId, userIds, deleteType);

    return reply.status(200).send({
      success: true,
      message: `Friends deleted successfully (${deleteType === 0 ? 'two-way' : 'one-way'})`,
      data: response
    });
  } catch (error: any) {
    logger.error('Error deleting friends:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to delete friends'
    });
  }
};

export const deleteAllFriends = async (
  request: FastifyRequest<{ Body: { fromUserId: string; deleteType: 0 | 1 } }>,
  reply: FastifyReply
): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { fromUserId, deleteType } = request.body;

  if (!fromUserId || deleteType === undefined) {
    return reply.status(400).send({
      success: false,
      message: 'From user ID and delete type are required'
    });
  }

  if (![0, 1].includes(deleteType)) {
    return reply.status(400).send({
      success: false,
      message: 'Delete type must be 0 (two-way) or 1 (one-way)'
    });
  }

  try {
    const response = await zengoService.deleteAllFriends(fromUserId, deleteType);

    return reply.status(200).send({
      success: true,
      message: `All friends deleted successfully (${deleteType === 0 ? 'two-way' : 'one-way'})`,
      data: response
    });
  } catch (error: any) {
    logger.error('Error deleting all friends:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to delete all friends'
    });
  }
};

export const queryFriendList = async (
  request: FastifyRequest<{ Querystring: { fromUserId: string; limit?: string; next?: string } }>,
  reply: FastifyReply
): Promise<any> => {
  const { fromUserId, limit, next } = request.query;

  if (!fromUserId) {
    return reply.status(400).send({
      success: false,
      message: 'From user ID is required'
    });
  }

  try {
    const limitNum = limit ? parseInt(limit) : 100;
    const nextNum = next ? parseInt(next) : 0;

    const response: ZengoQueryFriendListResponse = await zengoService.queryFriendList(fromUserId, limitNum, nextNum);

    return reply.status(200).send({
      success: true,
      message: 'Friend list retrieved successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error querying friend list:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to query friend list'
    });
  }
};

export const updateFriendsAlias = async (
  request: FastifyRequest<{ Body: { fromUserId: string; aliasUpdates: ZengoFriendAliasUpdate[] } }>,
  reply: FastifyReply
): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { fromUserId, aliasUpdates } = request.body;

  if (!fromUserId || !aliasUpdates) {
    return reply.status(400).send({
      success: false,
      message: 'From user ID and alias updates array are required'
    });
  }

  try {
    const response = await zengoService.updateFriendsAlias(fromUserId, aliasUpdates);

    return reply.status(200).send({
      success: true,
      message: 'Friend aliases updated successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error updating friend aliases:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to update friend aliases'
    });
  }
};

export const updateFriendAttributes = async (
  request: FastifyRequest<{ Body: { fromUserId: string; userId: string; attributes: ZengoFriendAttribute[]; action?: number } }>,
  reply: FastifyReply
): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { fromUserId, userId, attributes, action } = request.body;

  if (!fromUserId || !userId || !attributes) {
    return reply.status(400).send({
      success: false,
      message: 'From user ID, user ID, and attributes are required'
    });
  }

  try {
    const response = await zengoService.updateFriendAttributes(fromUserId, userId, attributes, action);

    return reply.status(200).send({
      success: true,
      message: 'Friend attributes updated successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error updating friend attributes:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to update friend attributes'
    });
  }
};

export const blockUsers = async (
  request: FastifyRequest<{ Body: { fromUserId: string; userIds: string[] } }>,
  reply: FastifyReply
): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { fromUserId, userIds } = request.body;

  if (!fromUserId || !userIds) {
    return reply.status(400).send({
      success: false,
      message: 'From user ID and user IDs array are required'
    });
  }

  try {
    const response = await zengoService.blockUsers(fromUserId, userIds);

    return reply.status(200).send({
      success: true,
      message: 'Users blocked successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error blocking users:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to block users'
    });
  }
};

export const unblockUsers = async (
  request: FastifyRequest<{ Body: { fromUserId: string; userIds: string[] } }>,
  reply: FastifyReply
): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { fromUserId, userIds } = request.body;

  if (!fromUserId || !userIds) {
    return reply.status(400).send({
      success: false,
      message: 'From user ID and user IDs array are required'
    });
  }

  try {
    const response = await zengoService.unblockUsers(fromUserId, userIds);

    return reply.status(200).send({
      success: true,
      message: 'Users unblocked successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error unblocking users:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to unblock users'
    });
  }
};

export const queryBlocklist = async (
  request: FastifyRequest<{ Querystring: { fromUserId: string; limit?: string; next?: string } }>,
  reply: FastifyReply
): Promise<any> => {
  const { fromUserId, limit, next } = request.query;

  if (!fromUserId) {
    return reply.status(400).send({
      success: false,
      message: 'From user ID is required'
    });
  }

  try {
    const limitNum = limit ? parseInt(limit) : 100;
    const nextNum = next ? parseInt(next) : 0;

    const response: ZengoQueryBlocklistResponse = await zengoService.queryBlocklist(fromUserId, limitNum, nextNum);

    return reply.status(200).send({
      success: true,
      message: 'Blocklist retrieved successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error querying blocklist:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to query blocklist'
    });
  }
};

export const checkBlockship = async (
  request: FastifyRequest<{ Body: { fromUserId: string; userIds: string[] } }>,
  reply: FastifyReply
): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { fromUserId, userIds } = request.body;

  if (!fromUserId || !userIds) {
    return reply.status(400).send({
      success: false,
      message: 'From user ID and user IDs array are required'
    });
  }

  try {
    const response: ZengoCheckBlockshipResponse = await zengoService.checkBlockship(fromUserId, userIds);

    return reply.status(200).send({
      success: true,
      message: 'Blockship check completed successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error checking blockship:', error);

    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'Failed to check blockship'
    });
  }
};
 