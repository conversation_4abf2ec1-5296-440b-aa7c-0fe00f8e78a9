import mongoose, { Schema, Document } from 'mongoose';

export interface ICallRecord {
  contact_id: string;
  call_type: 'audio' | 'video';
  duration_seconds: number;
  timestamp: Date;
  status: 'completed' | 'missed' | 'declined';
}

export interface IVideoRecord {
  contact_id: string;
  duration_seconds: number;
  timestamp: Date;
  quality: string;
}

export interface IChatRecord {
  contact_id: string;
  last_message: string;
  last_message_timestamp: Date;
  unread_count: number;
}

export interface ITokenRecord {
  token_id: string;
  token_type: 'access' | 'refresh';
  expires_at: Date;
  created_at: Date;
  revoked: boolean;
}

export interface IDeviceRecord {
  device_id: string;
  device_name: string;
  device_type: 'ios' | 'android' | 'web' | 'desktop';
  device_fingerprint: string;
  identity_key: string;
  signed_pre_key: string;
  pre_key_bundle: string;
  registration_id: string;
  is_primary: boolean;
  is_active: boolean;
  last_seen: Date;
  ip_address?: string;
  user_agent?: string;
  app_version?: string | undefined;
  os_version?: string | undefined;
  last_key_update?: number;
  linked_at: Date;
  verified_at?: Date;
}

export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  email?: string;
  username?: string;
  phone: string;
  profile_picture?: string;
  is_verified: boolean;
  last_seen?: Date;
  contacts: string[];
  blocked_users: string[];
  status?: string;
  calls: ICallRecord[];
  videos: IVideoRecord[];
  chats: IChatRecord[];
  tokens: ITokenRecord[];
  bio?: string;
  address?: string;
  identity_key?: string;
  signed_pre_key?: string;
  pre_key?: string;
  one_time_keys: string[];
  last_key_update?: number;
  device_id?: string;
  registration_id?: string;
  devices: IDeviceRecord[];
  // Status privacy settings
  status_privacy_setting?: 'Everyone' | 'Contacts' | 'ContactsExcept' | 'OnlyShare';
  allowed_contacts?: string[];
  blocked_contacts?: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

const CallRecordSchema = new Schema<ICallRecord>({
  contact_id: { type: String, required: true },
  call_type: { type: String, enum: ['audio', 'video'], required: true },
  duration_seconds: { type: Number, required: true },
  timestamp: { type: Date, required: true, default: Date.now },
  status: { type: String, enum: ['completed', 'missed', 'declined'], required: true }
});

const VideoRecordSchema = new Schema<IVideoRecord>({
  contact_id: { type: String, required: true },
  duration_seconds: { type: Number, required: true },
  timestamp: { type: Date, required: true, default: Date.now },
  quality: { type: String, required: true }
});

const ChatRecordSchema = new Schema<IChatRecord>({
  contact_id: { type: String, required: true },
  last_message: { type: String, required: true },
  last_message_timestamp: { type: Date, required: true, default: Date.now },
  unread_count: { type: Number, default: 0 }
});

const TokenRecordSchema = new Schema<ITokenRecord>({
  token_id: { type: String, required: true },
  token_type: { type: String, enum: ['access', 'refresh'], required: true },
  expires_at: { type: Date, required: true },
  created_at: { type: Date, required: true, default: Date.now },
  revoked: { type: Boolean, default: false }
});

const DeviceRecordSchema = new Schema<IDeviceRecord>({
  device_id: { type: String, required: true, unique: true },
  device_name: { type: String, required: true },
  device_type: { type: String, enum: ['ios', 'android', 'web', 'desktop'], required: true },
  device_fingerprint: { type: String, required: true },
  identity_key: { type: String, required: true },
  signed_pre_key: { type: String, required: true },
  pre_key_bundle: { type: String, required: true },
  registration_id: { type: String, required: true },
  is_primary: { type: Boolean, default: false },
  is_active: { type: Boolean, default: true },
  last_seen: { type: Date, default: Date.now },
  ip_address: { type: String },
  user_agent: { type: String },
  app_version: { type: String },
  os_version: { type: String },
  linked_at: { type: Date, default: Date.now },
  verified_at: { type: Date }
});

const UserSchema = new Schema<IUser>({
  email: { type: String, sparse: true },
  username: { type: String, sparse: true },
  phone: { type: String, required: true, unique: true },
  profile_picture: { type: String },
  is_verified: { type: Boolean, default: false },
  last_seen: { type: Date, default: Date.now },
  contacts: [{ type: String }],
  blocked_users: [{ type: String }],
  status: { type: String },
  calls: [CallRecordSchema],
  videos: [VideoRecordSchema],
  chats: [ChatRecordSchema],
  tokens: [TokenRecordSchema],
  devices: [DeviceRecordSchema],
  bio: { type: String },
  address: { type: String },
  identity_key: { type: String },
  signed_pre_key: { type: String },
  pre_key: { type: String },
  one_time_keys: [{ type: String }],
  last_key_update: { type: Number },
  device_id: { type: String },
  registration_id: { type: String },
  // Status privacy settings
  status_privacy_setting: {
    type: String,
    enum: ['Everyone', 'Contacts', 'ContactsExcept', 'OnlyShare'],
    default: 'Contacts'
  },
  allowed_contacts: [{ type: String }],
  blocked_contacts: [{ type: String }]
}, {
  timestamps: true
});

export const User = mongoose.model<IUser>('User', UserSchema);
