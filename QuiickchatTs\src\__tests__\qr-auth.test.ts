import { QRAuthService } from '../services/qr-auth.service';
import { DeviceLinkService } from '../services/device-link.service';
import { CryptoService } from '../services/crypto.service';
import { QRSession } from '../models/qr-session.model';
import { DeviceLinkSession } from '../models/device-link.model';
import { User } from '../models/user.model';
import { connectDatabase } from '../config/database';
import mongoose from 'mongoose';

describe('QR Authentication System', () => {
  beforeAll(async () => {
    await connectDatabase();
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    await QRSession.deleteMany({});
    await DeviceLinkSession.deleteMany({});
    await User.deleteMany({});
  });

  describe('QRAuthService', () => {
    describe('generateQRCode', () => {
      it('should generate a valid QR code for login', async () => {
        const options = {
          sessionType: 'login' as const,
          initiatorIp: '***********',
          initiatorUserAgent: 'Mozilla/5.0 Test Browser',
          initiatorFingerprint: 'test-fingerprint-123'
        };

        const result = await QRAuthService.generateQRCode(options);

        expect(result).toHaveProperty('qrCode');
        expect(result).toHaveProperty('sessionId');
        expect(result).toHaveProperty('expiresAt');
        expect(result.qrCode).toMatch(/^data:image\/png;base64,/);
        expect(result.sessionId).toHaveLength(64);
        expect(result.expiresAt).toBeInstanceOf(Date);
      });

      it('should generate a valid QR code for device linking', async () => {
        const options = {
          sessionType: 'device_link' as const,
          initiatorIp: '***********',
          initiatorUserAgent: 'Mozilla/5.0 Test Browser',
          initiatorFingerprint: 'test-fingerprint-123'
        };

        const result = await QRAuthService.generateQRCode(options);

        expect(result).toHaveProperty('qrCode');
        expect(result).toHaveProperty('sessionId');
        expect(result).toHaveProperty('expiresAt');
      });

      it('should enforce rate limiting', async () => {
        const options = {
          sessionType: 'login' as const,
          initiatorIp: '***********',
          initiatorUserAgent: 'Mozilla/5.0 Test Browser',
          initiatorFingerprint: 'test-fingerprint-123'
        };

        // Generate 5 QR codes (should be allowed)
        for (let i = 0; i < 5; i++) {
          await QRAuthService.generateQRCode(options);
        }

        // 6th request should fail
        await expect(QRAuthService.generateQRCode(options))
          .rejects.toThrow('Rate limit exceeded');
      });
    });

    describe('scanQRCode', () => {
      it('should successfully scan a valid QR code', async () => {
        // First generate a QR code
        const generateResult = await QRAuthService.generateQRCode({
          sessionType: 'login',
          initiatorIp: '***********',
          initiatorUserAgent: 'Mozilla/5.0 Test Browser',
          initiatorFingerprint: 'test-fingerprint-123'
        });

        // Create test user
        const user = new User({
          phone: '+1234567890',
          is_verified: true,
          devices: []
        });
        await user.save();

        // Extract QR data from the generated QR code
        const session = await QRSession.findOne({ session_id: generateResult.sessionId });
        const qrData = session!.qr_code_data;

        const scanResult = await QRAuthService.scanQRCode(
          qrData,
          '+1234567890',
          'device-123',
          '***********',
          'Mozilla/5.0 Mobile Browser'
        );

        expect(scanResult.isValid).toBe(true);
        expect(scanResult.sessionId).toBe(generateResult.sessionId);
        expect(scanResult.sessionType).toBe('login');
        expect(scanResult).toHaveProperty('challenge');
        expect(scanResult).toHaveProperty('nonce');
      });

      it('should reject invalid QR data', async () => {
        await expect(QRAuthService.scanQRCode(
          'invalid-qr-data',
          '+1234567890',
          'device-123',
          '***********',
          'Mozilla/5.0 Mobile Browser'
        )).rejects.toThrow('Invalid QR code format');
      });

      it('should reject expired QR codes', async () => {
        // Create an expired session
        const expiredSession = new QRSession({
          session_id: 'expired-session-123',
          qr_code_data: JSON.stringify({
            v: '1.0',
            t: 'login',
            s: 'expired-session-123',
            c: 'challenge',
            n: 'nonce',
            e: Math.floor((Date.now() - 60000) / 1000) // 1 minute ago
          }),
          session_type: 'login',
          status: 'pending',
          initiator_ip: '***********',
          initiator_user_agent: 'Test Browser',
          initiator_fingerprint: 'test-fingerprint',
          security_data: {
            nonce: 'nonce',
            challenge: 'challenge',
            salt: 'salt'
          },
          expires_at: new Date(Date.now() - 60000) // 1 minute ago
        });
        await expiredSession.save();

        await expect(QRAuthService.scanQRCode(
          expiredSession.qr_code_data,
          '+1234567890',
          'device-123',
          '***********',
          'Mozilla/5.0 Mobile Browser'
        )).rejects.toThrow('QR code has expired');
      });
    });

    describe('approveQRSession', () => {
      it('should approve a login session and return auth token', async () => {
        // Create a scanned session
        const session = new QRSession({
          session_id: 'test-session-123',
          qr_code_data: '{}',
          session_type: 'login',
          status: 'scanned',
          scanner_phone: '+1234567890',
          initiator_ip: '***********',
          initiator_user_agent: 'Test Browser',
          initiator_fingerprint: 'test-fingerprint',
          security_data: {
            nonce: 'nonce',
            challenge: 'challenge',
            salt: 'salt'
          },
          expires_at: new Date(Date.now() + 60000)
        });
        await session.save();

        const approvalData = {
          deviceName: 'Test Device',
          deviceType: 'web' as const,
          identityKey: 'identity-key',
          signedPreKey: 'signed-pre-key',
          preKeyBundle: 'pre-key-bundle',
          registrationId: 'registration-id',
          deviceFingerprint: 'device-fingerprint'
        };

        const result = await QRAuthService.approveQRSession(
          'test-session-123',
          approvalData,
          '+1234567890'
        );

        expect(result.success).toBe(true);
        expect(result.authToken).toBeDefined();
        expect(typeof result.authToken).toBe('string');
      });
    });
  });

  describe('DeviceLinkService', () => {
    describe('initiateLinking', () => {
      it('should initiate device linking successfully', async () => {
        // Create test user
        const user = new User({
          phone: '+1234567890',
          is_verified: true,
          devices: []
        });
        await user.save();

        const request = {
          userPhone: '+1234567890',
          primaryDeviceId: 'primary-device-123',
          deviceData: {
            deviceName: 'Test Device',
            deviceType: 'android' as const,
            deviceFingerprint: 'device-fingerprint-123',
            ipAddress: '***********',
            userAgent: 'Android App'
          },
          cryptoData: {
            identityKey: 'identity-key',
            signedPreKey: 'signed-pre-key',
            preKeyBundle: 'pre-key-bundle',
            registrationId: 'registration-id'
          }
        };

        const result = await DeviceLinkService.initiateLinking(request);

        expect(result).toHaveProperty('sessionId');
        expect(result).toHaveProperty('verificationCode');
        expect(result).toHaveProperty('expiresAt');
        expect(result.verificationCode).toMatch(/^\d{6}$/);
      });

      it('should reject linking when max devices reached', async () => {
        // Create user with maximum devices
        const devices = Array.from({ length: 5 }, (_, i) => ({
          device_id: `device-${i}`,
          device_name: `Device ${i}`,
          device_type: 'android' as const,
          device_fingerprint: `fingerprint-${i}`,
          identity_key: 'key',
          signed_pre_key: 'key',
          pre_key_bundle: 'bundle',
          registration_id: 'reg-id',
          is_primary: i === 0,
          is_active: true,
          last_seen: new Date(),
          linked_at: new Date(),
          verified_at: new Date()
        }));

        const user = new User({
          phone: '+1234567890',
          is_verified: true,
          devices
        });
        await user.save();

        const request = {
          userPhone: '+1234567890',
          primaryDeviceId: 'primary-device-123',
          deviceData: {
            deviceName: 'Test Device',
            deviceType: 'android' as const,
            deviceFingerprint: 'new-device-fingerprint',
            ipAddress: '***********',
            userAgent: 'Android App'
          },
          cryptoData: {
            identityKey: 'identity-key',
            signedPreKey: 'signed-pre-key',
            preKeyBundle: 'pre-key-bundle',
            registrationId: 'registration-id'
          }
        };

        await expect(DeviceLinkService.initiateLinking(request))
          .rejects.toThrow('Maximum of 5 devices allowed per user');
      });
    });
  });

  describe('CryptoService', () => {
    describe('generateIdentityKeyPair', () => {
      it('should generate a valid identity key pair', () => {
        const keyPair = CryptoService.generateIdentityKeyPair();

        expect(keyPair).toHaveProperty('publicKey');
        expect(keyPair).toHaveProperty('privateKey');
        expect(keyPair.publicKey).toBeInstanceOf(Uint8Array);
        expect(keyPair.privateKey).toBeInstanceOf(Uint8Array);
        expect(keyPair.publicKey.length).toBe(32);
        expect(keyPair.privateKey.length).toBe(32);
      });
    });

    describe('generateDeviceFingerprint', () => {
      it('should generate consistent fingerprints for same input', () => {
        const identityKey = new Uint8Array(32);
        const deviceInfo = { name: 'Test Device', type: 'android' };

        const fingerprint1 = CryptoService.generateDeviceFingerprint(identityKey, deviceInfo);
        const fingerprint2 = CryptoService.generateDeviceFingerprint(identityKey, deviceInfo);

        expect(fingerprint1).toBe(fingerprint2);
        expect(fingerprint1).toHaveLength(64); // 32 bytes as hex
      });

      it('should generate different fingerprints for different inputs', () => {
        const identityKey1 = new Uint8Array(32);
        const identityKey2 = new Uint8Array(32);
        identityKey2[0] = 1; // Make it different

        const deviceInfo = { name: 'Test Device', type: 'android' };

        const fingerprint1 = CryptoService.generateDeviceFingerprint(identityKey1, deviceInfo);
        const fingerprint2 = CryptoService.generateDeviceFingerprint(identityKey2, deviceInfo);

        expect(fingerprint1).not.toBe(fingerprint2);
      });
    });

    describe('encryptMessage and decryptMessage', () => {
      it('should encrypt and decrypt messages correctly', () => {
        const messageKey = CryptoService.generateSecureRandom(32);
        const plaintext = new Uint8Array(Buffer.from('Hello, World!', 'utf8'));
        const associatedData = new Uint8Array(Buffer.from('metadata', 'utf8'));

        const encrypted = CryptoService.encryptMessage(messageKey, plaintext, associatedData);
        const decrypted = CryptoService.decryptMessage(messageKey, encrypted.ciphertext, encrypted.tag, associatedData);

        expect(Buffer.from(decrypted).toString('utf8')).toBe('Hello, World!');
      });

      it('should fail decryption with wrong key', () => {
        const messageKey1 = CryptoService.generateSecureRandom(32);
        const messageKey2 = CryptoService.generateSecureRandom(32);
        const plaintext = new Uint8Array(Buffer.from('Hello, World!', 'utf8'));
        const associatedData = new Uint8Array(Buffer.from('metadata', 'utf8'));

        const encrypted = CryptoService.encryptMessage(messageKey1, plaintext, associatedData);

        expect(() => {
          CryptoService.decryptMessage(messageKey2, encrypted.ciphertext, encrypted.tag, associatedData);
        }).toThrow('Message decryption failed');
      });
    });
  });
});
