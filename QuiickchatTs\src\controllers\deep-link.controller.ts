import { FastifyRequest, FastifyReply } from 'fastify';
import { DeepLinkService } from '../services/deep-link.service';
import * as qrAuthService from '../services/qr-auth.service';
import * as deviceLinkService from '../services/device-link.service';
import { ValidationError, NotFoundError } from '../utils/errors';
import { logger } from '../utils/logger';
import { ApiResponse } from '../types';

interface GenerateDeepLinkRequest {
  action: 'qr_scan' | 'device_link' | 'login_approve' | 'device_verify';
  session_id: string;
  metadata?: Record<string, any>;
}

interface ParseDeepLinkRequest {
  url: string;
}

export const generateDeepLink = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const { action, session_id, metadata } = request.body as GenerateDeepLinkRequest;

    if (!action || !session_id) {
      throw new ValidationError('Action and session ID are required');
    }

    let deepLinkData;

    switch (action) {
      case 'qr_scan':
        const qrSession = await qrAuthService.getSessionStatus(session_id);
        if (qrSession.status !== 'pending') {
          throw new ValidationError('QR session is not in pending state');
        }
        
        deepLinkData = DeepLinkService.generateQRScanDeepLink(
          session_id,
          metadata?.challenge || '',
          metadata?.nonce || ''
        );
        break;

      case 'device_link':
        const linkValidation = await DeepLinkService.validateDeviceLinkDeepLink(session_id);
        if (!linkValidation.isValid) {
          throw new ValidationError('Device link session is not valid');
        }
        
        deepLinkData = DeepLinkService.generateDeviceLinkDeepLink(
          session_id,
          metadata?.verificationCode || ''
        );
        break;

      case 'login_approve':
        deepLinkData = DeepLinkService.generateLoginApprovalDeepLink(session_id, metadata || {});
        break;

      case 'device_verify':
        deepLinkData = DeepLinkService.generateDeviceVerificationDeepLink(session_id, metadata || {});
        break;

      default:
        throw new ValidationError('Invalid action type');
    }

    const response: ApiResponse = {
      success: true,
      message: 'Deep link generated successfully',
      data: {
        deep_link: deepLinkData.url,
        ios_url: deepLinkData.iosUrl,
        android_url: deepLinkData.androidUrl,
        web_fallback: deepLinkData.webFallback,
        action,
        session_id
      }
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error generating deep link:', error);
    throw error;
  }
};

export const parseDeepLink = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const { url } = request.body as ParseDeepLinkRequest;

    if (!url) {
      throw new ValidationError('URL is required');
    }

    const parsedData = await DeepLinkService.parseDeepLink(url);

    const response: ApiResponse = {
      success: true,
      message: 'Deep link parsed successfully',
      data: {
        action: parsedData.action,
        session_id: parsedData.sessionId,
        challenge: parsedData.challenge,
        nonce: parsedData.nonce,
        expires_at: parsedData.expiresAt,
        metadata: parsedData.metadata
      }
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error parsing deep link:', error);
    throw error;
  }
};

export const handleQRScanRedirect = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const { sessionId } = request.params as { sessionId: string };

    if (!sessionId) {
      throw new ValidationError('Session ID is required');
    }

    const validation = await DeepLinkService.validateQRScanDeepLink(sessionId);
    if (!validation.isValid) {
      throw new NotFoundError('QR session not found or expired');
    }

    const session = validation.session;
    const deepLinkData = DeepLinkService.generateQRScanDeepLink(
      sessionId,
      session.security_data.challenge,
      session.security_data.nonce
    );

    const html = DeepLinkService.generateUniversalLinkHTML(
      deepLinkData,
      'QuickChat QR Authentication',
      'Scan this QR code with your QuickChat mobile app to authenticate'
    );

    reply.type('text/html').send(html);
  } catch (error) {
    logger.error('Error handling QR scan redirect:', error);
    
    const errorHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>QuickChat - Error</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
      </head>
      <body style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <h1>Error</h1>
        <p>The QR code has expired or is invalid.</p>
        <p><a href="${process.env.WEB_FALLBACK_URL || 'https://quickchat.app'}">Return to QuickChat</a></p>
      </body>
      </html>
    `;
    
    reply.status(400).type('text/html').send(errorHtml);
  }
};

export const handleDeviceLinkRedirect = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const { sessionId } = request.params as { sessionId: string };

    if (!sessionId) {
      throw new ValidationError('Session ID is required');
    }

    const validation = await DeepLinkService.validateDeviceLinkDeepLink(sessionId);
    if (!validation.isValid) {
      throw new NotFoundError('Device link session not found or expired');
    }

    const session = validation.session;
    const deepLinkData = DeepLinkService.generateDeviceLinkDeepLink(
      sessionId,
      session.verification_data.verification_code
    );

    const html = DeepLinkService.generateUniversalLinkHTML(
      deepLinkData,
      'QuickChat Device Linking',
      'Complete device linking with your QuickChat mobile app'
    );

    reply.type('text/html').send(html);
  } catch (error) {
    logger.error('Error handling device link redirect:', error);
    
    const errorHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>QuickChat - Error</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
      </head>
      <body style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <h1>Error</h1>
        <p>The device linking session has expired or is invalid.</p>
        <p><a href="${process.env.WEB_FALLBACK_URL || 'https://quickchat.app'}">Return to QuickChat</a></p>
      </body>
      </html>
    `;
    
    reply.status(400).type('text/html').send(errorHtml);
  }
};

export const getAppStoreLinks = async (_request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const appStoreLinks = DeepLinkService.generateAppStoreLinks();

    const response: ApiResponse = {
      success: true,
      message: 'App store links retrieved successfully',
      data: {
        ios_app_store: appStoreLinks.ios,
        android_play_store: appStoreLinks.android
      }
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error getting app store links:', error);
    throw error;
  }
};

export const generateSmartBanner = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const { sessionId, action } = request.params as { sessionId: string; action: string };

    if (!sessionId || !action) {
      throw new ValidationError('Session ID and action are required');
    }

    const smartBanner = DeepLinkService.generateSmartBanner(sessionId, action);

    reply.type('text/html').send(smartBanner);
  } catch (error) {
    logger.error('Error generating smart banner:', error);
    throw error;
  }
};

export const validateDeepLinkSession = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const { sessionId, action } = request.params as { sessionId: string; action: string };

    if (!sessionId || !action) {
      throw new ValidationError('Session ID and action are required');
    }

    let isValid = false;
    let sessionData = null;

    switch (action) {
      case 'qr-scan':
        const qrValidation = await DeepLinkService.validateQRScanDeepLink(sessionId);
        isValid = qrValidation.isValid;
        sessionData = qrValidation.session;
        break;

      case 'device-link':
        const linkValidation = await DeepLinkService.validateDeviceLinkDeepLink(sessionId);
        isValid = linkValidation.isValid;
        sessionData = linkValidation.session;
        break;

      default:
        throw new ValidationError('Invalid action type');
    }

    const response: ApiResponse = {
      success: true,
      message: 'Deep link session validation completed',
      data: {
        is_valid: isValid,
        session_id: sessionId,
        action,
        session_data: sessionData ? {
          status: sessionData.status,
          expires_at: sessionData.expires_at,
          created_at: sessionData.created_at
        } : null
      }
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error validating deep link session:', error);
    throw error;
  }
};
