#!/usr/bin/env node

/**
 * QuickChat QR Code Decoder
 * 
 * This script decodes QR codes from QuickChat authentication system
 * and shows the internal data structure and security information.
 * 
 * Usage:
 *   node decode-qr.js                                    # Decode the provided QR code
 *   node decode-qr.js "base64_image_data"                # Decode custom base64 QR image
 *   node decode-qr.js '{"v":"1.0","t":"login",...}'      # Decode JSON QR data directly
 */

const Jimp = require('jimp');
const jsQR = require('jsqr');

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(level, message, data = null) {
  const colorMap = {
    info: colors.cyan,
    success: colors.green,
    warn: colors.yellow,
    error: colors.red,
    debug: colors.magenta
  };
  const color = colorMap[level] || colors.reset;
  console.log(`${color}${message}${colors.reset}`);
  
  if (data) {
    console.log(`${colors.bright}${JSON.stringify(data, null, 2)}${colors.reset}`);
  }
}

// Your provided QR code data
const providedQRCode = "data:image/png;base64,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";

async function decodeQRFromBase64(base64Data) {
  try {
    log('info', '🔍 Decoding QR code from base64 image...');
    
    // Remove data URL prefix if present
    const base64Image = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
    
    // Convert base64 to buffer
    const imageBuffer = Buffer.from(base64Image, 'base64');
    
    // Load image with Jimp
    const image = await Jimp.read(imageBuffer);
    const imageData = {
      data: new Uint8ClampedArray(image.bitmap.data),
      width: image.bitmap.width,
      height: image.bitmap.height
    };
    
    // Decode QR code
    const qrResult = jsQR(imageData.data, imageData.width, imageData.height);
    
    if (!qrResult) {
      log('error', '❌ Could not decode QR code from image');
      return null;
    }
    
    log('success', '✅ QR code decoded successfully');
    log('debug', `Raw QR data: ${qrResult.data}`);
    
    return qrResult.data;
  } catch (error) {
    log('error', `❌ Error decoding QR code: ${error.message}`);
    return null;
  }
}

function analyzeQRData(qrData) {
  log('info', '\n📊 Analyzing QR Code Data Structure...');
  
  try {
    const parsedData = JSON.parse(qrData);
    
    log('success', '✅ QR data is valid JSON');
    log('info', 'Raw QR Data:', parsedData);
    
    // Analyze each field
    log('info', '\n🔍 Field Analysis:');
    
    if (parsedData.v) {
      log('info', `📋 Version: ${parsedData.v} (Protocol version)`);
    }
    
    if (parsedData.t) {
      const typeMap = {
        'login': 'User Login Authentication',
        'device_link': 'Device Linking/Pairing'
      };
      log('info', `🎯 Type: ${parsedData.t} (${typeMap[parsedData.t] || 'Unknown'})`);
    }
    
    if (parsedData.s) {
      log('info', `🆔 Session ID: ${parsedData.s}`);
      log('debug', `   Length: ${parsedData.s.length} characters`);
      log('debug', `   Format: ${parsedData.s.startsWith('session-') ? 'Standard format' : 'Custom format'}`);
    }
    
    if (parsedData.c) {
      log('info', `🔐 Challenge: ${parsedData.c}`);
      log('debug', `   Length: ${parsedData.c.length} characters`);
      log('debug', `   Purpose: Cryptographic challenge for security verification`);
    }
    
    if (parsedData.n) {
      log('info', `🎲 Nonce: ${parsedData.n}`);
      log('debug', `   Length: ${parsedData.n.length} characters`);
      log('debug', `   Purpose: One-time number to prevent replay attacks`);
    }
    
    if (parsedData.e) {
      const expiryDate = new Date(parsedData.e * 1000);
      const now = new Date();
      const isExpired = expiryDate < now;
      const timeLeft = Math.max(0, Math.floor((expiryDate - now) / 1000));
      
      log('info', `⏰ Expiry: ${parsedData.e} (Unix timestamp)`);
      log('debug', `   Date: ${expiryDate.toISOString()}`);
      log('debug', `   Local: ${expiryDate.toLocaleString()}`);
      log(isExpired ? 'warn' : 'success', `   Status: ${isExpired ? '❌ EXPIRED' : '✅ Valid'}`);
      
      if (!isExpired) {
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;
        log('info', `   Time left: ${minutes}m ${seconds}s`);
      }
    }
    
    // Security analysis
    log('info', '\n🛡️ Security Analysis:');
    
    const securityScore = calculateSecurityScore(parsedData);
    log('info', `Security Score: ${securityScore.score}/100`);
    
    securityScore.issues.forEach(issue => {
      log('warn', `⚠️  ${issue}`);
    });
    
    securityScore.strengths.forEach(strength => {
      log('success', `✅ ${strength}`);
    });
    
    // Usage instructions
    log('info', '\n📱 Usage Instructions:');
    
    if (parsedData.t === 'login') {
      log('info', '1. 📱 Open QuickChat mobile app');
      log('info', '2. 🔍 Scan this QR code with the app');
      log('info', '3. ✅ Approve the login request on your mobile');
      log('info', '4. 🎉 Desktop/web client will be authenticated');
    } else if (parsedData.t === 'device_link') {
      log('info', '1. 📱 Open QuickChat mobile app');
      log('info', '2. 🔍 Scan this QR code with the app');
      log('info', '3. ✅ Approve device linking on your mobile');
      log('info', '4. 🔗 New device will be linked to your account');
    }
    
    return parsedData;
    
  } catch (error) {
    log('error', '❌ QR data is not valid JSON');
    log('debug', `Raw data: ${qrData}`);
    log('debug', `Error: ${error.message}`);
    return null;
  }
}

function calculateSecurityScore(data) {
  let score = 0;
  const issues = [];
  const strengths = [];
  
  // Check version
  if (data.v === '1.0') {
    score += 10;
    strengths.push('Uses current protocol version');
  } else {
    issues.push('Unknown or outdated protocol version');
  }
  
  // Check session ID
  if (data.s && data.s.length >= 20) {
    score += 20;
    strengths.push('Session ID has adequate length');
  } else {
    issues.push('Session ID too short or missing');
  }
  
  // Check challenge
  if (data.c && data.c.length >= 16) {
    score += 25;
    strengths.push('Cryptographic challenge present');
  } else {
    issues.push('Weak or missing cryptographic challenge');
  }
  
  // Check nonce
  if (data.n && data.n.length >= 8) {
    score += 20;
    strengths.push('Nonce present for replay protection');
  } else {
    issues.push('Weak or missing nonce');
  }
  
  // Check expiry
  if (data.e) {
    const expiryDate = new Date(data.e * 1000);
    const now = new Date();
    const timeLeft = (expiryDate - now) / 1000;
    
    if (timeLeft > 0 && timeLeft <= 300) { // 5 minutes max
      score += 25;
      strengths.push('Appropriate expiry time (short-lived)');
    } else if (timeLeft > 300) {
      score += 10;
      issues.push('Expiry time too long (security risk)');
    } else {
      issues.push('QR code has expired');
    }
  } else {
    issues.push('No expiry time specified');
  }
  
  return { score, issues, strengths };
}

async function main() {
  console.log(`${colors.bright}${colors.blue}🔍 QuickChat QR Code Decoder${colors.reset}`);
  console.log(`${colors.cyan}Analyzing QuickChat authentication QR codes${colors.reset}\n`);
  
  const input = process.argv[2];
  let qrData = null;
  
  if (input) {
    if (input.startsWith('data:image/') || input.match(/^[A-Za-z0-9+/]+=*$/)) {
      // Base64 image data
      log('info', 'Input detected as base64 image data');
      qrData = await decodeQRFromBase64(input);
    } else if (input.startsWith('{') && input.endsWith('}')) {
      // JSON data
      log('info', 'Input detected as JSON QR data');
      qrData = input;
    } else {
      log('error', 'Invalid input format. Expected base64 image or JSON data.');
      process.exit(1);
    }
  } else {
    // Use provided QR code
    log('info', 'Using provided QR code from QuickChat...');
    qrData = await decodeQRFromBase64(providedQRCode);
  }
  
  if (!qrData) {
    log('error', '❌ Failed to decode QR code');
    process.exit(1);
  }
  
  const parsedData = analyzeQRData(qrData);
  
  if (parsedData) {
    log('info', '\n🎯 Summary:');
    log('success', `✅ QR Code Type: ${parsedData.t || 'Unknown'}`);
    log('success', `✅ Session ID: ${parsedData.s || 'Unknown'}`);
    log('success', `✅ Protocol Version: ${parsedData.v || 'Unknown'}`);
    
    if (parsedData.e) {
      const expiryDate = new Date(parsedData.e * 1000);
      const isExpired = expiryDate < new Date();
      log(isExpired ? 'warn' : 'success', `${isExpired ? '⚠️' : '✅'} Expires: ${expiryDate.toLocaleString()}`);
    }
  }
  
  log('info', '\n📚 For more information about QuickChat QR authentication:');
  log('info', '   - Read: QR_AUTHENTICATION_README.md');
  log('info', '   - Test: node test-twilio.js');
  log('info', '   - Demo: http://localhost:8080');
}

// Check if required packages are available
try {
  require('jimp');
  require('jsqr');
} catch (error) {
  console.log(`${colors.red}❌ Missing required packages. Install with:${colors.reset}`);
  console.log(`${colors.yellow}npm install jimp jsqr${colors.reset}`);
  process.exit(1);
}

main().catch(error => {
  log('error', `💥 Unexpected error: ${error.message}`);
  console.error(error.stack);
  process.exit(1);
});
