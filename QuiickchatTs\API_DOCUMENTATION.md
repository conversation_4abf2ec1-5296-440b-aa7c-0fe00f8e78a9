# QuickChat API Documentation

## Overview
This document provides comprehensive examples for all enhanced request types in the QuickChat backend API.

## Base URL
```
Development: http://localhost:3000/api/v1
Production: https://your-domain.com/api/v1
```

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <your_access_token>
```

## Enhanced Request Types

### 1. Authentication Endpoints

#### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "phone": "+**********"
}
```

#### Verify Registration
```http
POST /auth/verify
Content-Type: application/json

{
  "phone": "+**********",
  "code": "123456"
}
```

#### Login User
```http
POST /auth/login
Content-Type: application/json

{
  "phone": "+**********"
}
```

#### Verify Login
```http
POST /auth/verify-login
Content-Type: application/json

{
  "phone": "+**********",
  "code": "123456"
}
```

#### Refresh Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refresh_token": "your_refresh_token_here"
}
```

### 2. Profile Management

#### Update Profile (Full)
```http
PUT /users/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "username": "john_doe_2024",
  "bio": "Software Engineer | Tech Enthusiast | Lagos Based Developer",
  "address": "Victoria Island, Lagos, Nigeria",
  "email": "<EMAIL>",
  "status": "Available for work",
  "device_id": "device_12345",
  "registration_id": "reg_67890"
}
```

#### Update Status Only
```http
PUT /users/status
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "Busy - In a meeting"
}
```

#### Update Location
```http
PUT /users/location
Authorization: Bearer <token>
Content-Type: application/json

{
  "address": "Ikeja, Lagos State, Nigeria",
  "latitude": 6.5244,
  "longitude": 3.3792
}
```

#### Upload Profile Picture
```http
POST /users/upload-profile-picture
Authorization: Bearer <token>
Content-Type: multipart/form-data

profilePicture: <image_file>
```

### 3. Contact Management

#### Add Single Contact
```http
POST /contacts/add
Authorization: Bearer <token>
Content-Type: application/json

{
  "phone_number": "+1987654321",
  "display_name": "Jane Smith"
}
```

#### Bulk Add Contacts
```http
POST /contacts/bulk
Authorization: Bearer <token>
Content-Type: application/json

{
  "contacts": [
    {
      "display_name": "John Smith",
      "phone_number": "+**********"
    },
    {
      "display_name": "Jane Doe",
      "phone_number": "+1987654321"
    },
    {
      "display_name": "Bob Johnson",
      "phone_number": "+1555123456"
    }
  ]
}
```

#### Update Contact
```http
PUT /contacts/update
Authorization: Bearer <token>
Content-Type: application/json

{
  "contact_id": "contact_123",
  "display_name": "Jane Smith Updated",
  "notes": "Work colleague"
}
```

#### Remove Contact
```http
DELETE /contacts/remove
Authorization: Bearer <token>
Content-Type: application/json

{
  "contact_id": "contact_123"
}
```

#### Search Users
```http
POST /users/search
Authorization: Bearer <token>
Content-Type: application/json

{
  "query": "john",
  "type": "username",
  "limit": 20
}
```

### 4. User Management

#### Block User
```http
POST /users/block
Authorization: Bearer <token>
Content-Type: application/json

{
  "user_id": "user_123",
  "reason": "Spam messages"
}
```

#### Unblock User
```http
POST /users/unblock
Authorization: Bearer <token>
Content-Type: application/json

{
  "user_id": "user_123"
}
```

#### Report User
```http
POST /users/report
Authorization: Bearer <token>
Content-Type: application/json

{
  "reported_user_id": "user_456",
  "reason": "harassment",
  "description": "User is sending inappropriate messages",
  "evidence_urls": ["https://example.com/screenshot1.jpg"]
}
```

### 5. Status Management

#### Create Text Status
```http
POST /api/v1/status/
Authorization: Bearer <token>
Content-Type: application/json

{
  "content_type": "text",
  "content": "Having a great day! 😊",
  "background_color": "#FF6B6B",
  "text_color": "#FFFFFF",
  "font_style": "bold",
  "privacy_setting": "Contacts"
}
```

#### Create Image/Video Status
```http
POST /api/v1/status/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

content_type: image
caption: Beautiful sunset today!
privacy_setting: Contacts
statusMedia: <image_file>
```

#### Get Contact Statuses (Status Feed)
```http
GET /api/v1/status/feed
Authorization: Bearer <token>
```

#### Get My Statuses
```http
GET /api/v1/status/my
Authorization: Bearer <token>
```

#### Get Statuses for a Specific User
```http
GET /api/v1/status/user/{user_id}
Authorization: Bearer <token>
```

#### View Status (Mark as Viewed)
```http
POST /api/v1/status/view
Authorization: Bearer <token>
Content-Type: application/json

{
  "status_id": "status_123"
}
```

#### Get Who Viewed a Specific Status
```http
GET /api/v1/status/{status_id}/views
Authorization: Bearer <token>
```

#### Delete a Status
```http
DELETE /api/v1/status/{status_id}
Authorization: Bearer <token>
```

#### Get Privacy Settings
```http
GET /api/v1/status/privacy
Authorization: Bearer <token>
```

#### Update Privacy Settings
```http
PUT /api/v1/status/privacy
Authorization: Bearer <token>
Content-Type: application/json

{
  "default_privacy_setting": "Contacts",
  "blocked_contacts": ["user_456"],
  "allowed_contacts": ["user_789", "user_101"]
}
```

#### Status Response Examples

**Create Status Response:**
```json
{
  "success": true,
  "message": "Status created successfully",
  "data": {
    "id": "status_123",
    "user_id": "user_456",
    "content_type": "text",
    "content": "Having a great day! 😊",
    "background_color": "#FF6B6B",
    "text_color": "#FFFFFF",
    "font_style": "bold",
    "privacy_setting": "Contacts",
    "view_count": 0,
    "is_active": true,
    "expires_at": "2024-01-02T12:00:00Z",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

**Status Feed Response:**
```json
{
  "success": true,
  "message": "Contact statuses retrieved successfully",
  "data": [
    {
      "user_id": "user_789",
      "display_name": "John Doe",
      "phone_number": "+**********",
      "profile_picture": "https://example.com/profile.jpg",
      "total_statuses": 3,
      "unviewed_statuses": 2,
      "latest_status_time": "2024-01-01T10:30:00Z"
    }
  ]
}
```

**Status Views Response:**
```json
{
  "success": true,
  "message": "Status views retrieved successfully",
  "data": [
    {
      "viewer_id": "user_101",
      "viewed_at": "2024-01-01T13:45:00Z",
      "display_name": "Jane Smith",
      "phone_number": "+0987654321",
      "profile_picture": "https://example.com/jane.jpg"
    }
  ]
}
```

**Privacy Settings Response:**
```json
{
  "success": true,
  "message": "Privacy settings retrieved successfully",
  "data": {
    "default_privacy_setting": "Contacts",
    "blocked_contacts": ["user_456"],
    "allowed_contacts": ["user_789", "user_101"]
  }
}
```

### 6. Media Upload for Chat

#### Upload Single Media File
```http
POST /api/v1/upload/chat-media
Authorization: Bearer <token>
Content-Type: multipart/form-data

Form Data:
- file: [media file]
```

**Supported File Types:**
- Images: JPEG, PNG, GIF, WebP (max 10MB)
- Videos: MP4, MPEG, QuickTime, WebM (max 100MB)
- Audio: MP3, WAV, OGG (max 50MB)
- Documents: PDF, TXT (max 25MB)

**Response:**
```json
{
  "success": true,
  "message": "Media uploaded successfully",
  "data": {
    "url": "https://res.cloudinary.com/dpwisibis/image/upload/v**********/chat-media/+**********/abc123.jpg",
    "public_id": "chat-media/+**********/abc123",
    "media_type": "image",
    "file_size": 1048576,
    "mime_type": "image/jpeg",
    "filename": "photo.jpg",
    "uploaded_at": "2023-11-20T10:30:00Z"
  }
}
```

#### Upload Multiple Media Files
```http
POST /api/v1/upload/chat-media/multiple
Authorization: Bearer <token>
Content-Type: multipart/form-data

Form Data:
- files: [multiple media files] (max 10 files)
```

**Response:**
```json
{
  "success": true,
  "message": "5 files uploaded successfully",
  "data": {
    "files": [
      {
        "url": "https://res.cloudinary.com/dpwisibis/image/upload/v**********/chat-media/+**********/abc123.jpg",
        "public_id": "chat-media/+**********/abc123",
        "media_type": "image",
        "file_size": 1048576,
        "mime_type": "image/jpeg",
        "filename": "photo1.jpg",
        "uploaded_at": "2023-11-20T10:30:00Z"
      }
    ],
    "total_uploaded": 5
  }
}
```

#### Delete Uploaded Media
```http
DELETE /api/v1/upload/chat-media/{public_id}
Authorization: Bearer <token>
```

### 7. Communication

#### Initiate Call
```http
POST /calls/initiate
Authorization: Bearer <token>
Content-Type: application/json

{
  "contact_id": "contact_123",
  "call_type": "video"
}
```

#### End Call
```http
POST /calls/end
Authorization: Bearer <token>
Content-Type: application/json

{
  "call_id": "call_456",
  "duration_seconds": 300,
  "status": "completed"
}
```

#### Send Message
```http
POST /messages/send
Authorization: Bearer <token>
Content-Type: application/json

{
  "contact_id": "contact_123",
  "message": "Hello! How are you?",
  "message_type": "text"
}
```

#### Send Media Message
```http
POST /messages/send
Authorization: Bearer <token>
Content-Type: multipart/form-data

contact_id: contact_123
message: Check out this photo!
message_type: image
media_file: <image_file>
```

### 7. Settings & Privacy

#### Update Notification Settings
```http
PUT /users/settings/notifications
Authorization: Bearer <token>
Content-Type: application/json

{
  "message_notifications": true,
  "call_notifications": true,
  "group_notifications": false,
  "status_notifications": true,
  "sound_enabled": true,
  "vibration_enabled": false,
  "notification_tone": "default"
}
```

#### Update Privacy Settings
```http
PUT /users/settings/privacy
Authorization: Bearer <token>
Content-Type: application/json

{
  "profile_photo_visibility": "contacts",
  "last_seen_visibility": "contacts",
  "status_visibility": "everyone",
  "read_receipts_enabled": true,
  "typing_indicators_enabled": true
}
```

### 8. Device & Security

#### Update Device Info
```http
PUT /users/device
Authorization: Bearer <token>
Content-Type: application/json

{
  "device_id": "device_12345",
  "device_name": "iPhone 15 Pro",
  "device_type": "ios",
  "app_version": "1.2.3",
  "os_version": "17.1",
  "push_token": "push_token_abc123"
}
```

#### Update Encryption Keys
```http
PUT /users/encryption-keys
Authorization: Bearer <token>
Content-Type: application/json

{
  "identity_key": "identity_key_base64",
  "signed_pre_key": "signed_pre_key_base64",
  "pre_key": "pre_key_base64",
  "one_time_keys": ["otk1_base64", "otk2_base64", "otk3_base64"]
}
```

### 9. System & Admin

#### Health Check
```http
GET /system/health
```

#### SMS Service Status
```http
GET /system/sms-status
Authorization: Bearer <token>
```

#### Test SMS Service
```http
POST /system/test-sms
Authorization: Bearer <token>
Content-Type: application/json

{
  "phone": "+**********"
}
```

#### Submit Feedback
```http
POST /system/feedback
Authorization: Bearer <token>
Content-Type: application/json

{
  "type": "bug",
  "title": "App crashes on startup",
  "description": "The app crashes immediately after opening on iOS 17",
  "category": "mobile",
  "priority": "high",
  "attachments": ["https://example.com/crash-log.txt"]
}
```

### 10. Data Management

#### Export User Data
```http
POST /users/export-data
Authorization: Bearer <token>
Content-Type: application/json

{
  "data_types": ["profile", "contacts", "messages", "calls"],
  "format": "json"
}
```

#### Create Backup
```http
POST /users/backup
Authorization: Bearer <token>
Content-Type: application/json

{
  "include_media": true,
  "backup_password": "secure_password_123"
}
```

#### Restore Backup
```http
POST /users/restore-backup
Authorization: Bearer <token>
Content-Type: application/json

{
  "backup_file_url": "https://backup-storage.com/user123/backup.zip",
  "backup_password": "secure_password_123"
}
```

#### Change Phone Number
```http
POST /users/change-phone
Authorization: Bearer <token>
Content-Type: application/json

{
  "new_phone": "+**********",
  "verification_code": "123456"
}
```

#### Delete Account
```http
DELETE /users/delete-account
Authorization: Bearer <token>
Content-Type: application/json

{
  "confirmation_text": "DELETE MY ACCOUNT",
  "reason": "No longer using the service"
}
```

### 11. Group Management

#### Create Group
```http
POST /groups/create
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Family Group",
  "description": "Our family chat group",
  "participants": ["user_123", "user_456", "user_789"],
  "group_picture": "https://example.com/group-photo.jpg"
}
```

#### Update Group
```http
PUT /groups/update
Authorization: Bearer <token>
Content-Type: application/json

{
  "group_id": "group_123",
  "name": "Updated Family Group",
  "description": "Updated description",
  "group_picture": "https://example.com/new-group-photo.jpg"
}
```

#### Add Group Members
```http
POST /groups/add-members
Authorization: Bearer <token>
Content-Type: application/json

{
  "group_id": "group_123",
  "user_ids": ["user_999", "user_888"]
}
```

#### Remove Group Members
```http
POST /groups/remove-members
Authorization: Bearer <token>
Content-Type: application/json

{
  "group_id": "group_123",
  "user_ids": ["user_456"]
}
```

#### Leave Group
```http
POST /groups/leave
Authorization: Bearer <token>
Content-Type: application/json

{
  "group_id": "group_123"
}
```

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "code": "ERROR_CODE",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "details": {
      // Additional error details
    }
  }
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (Validation Error)
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict (e.g., user already exists)
- `429` - Too Many Requests
- `500` - Internal Server Error
- `503` - Service Unavailable

## QR Code Authentication

### Generate QR Code
**POST** `/api/v1/qr/generate`

Generate a QR code for login or device linking.

**Headers:**
- `X-Device-Fingerprint`: Device fingerprint (required)

**Request Body:**
```json
{
  "session_type": "login|device_link",
  "device_fingerprint": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "QR code generated successfully",
  "data": {
    "qr_code": "data:image/png;base64,...",
    "session_id": "string",
    "expires_at": "2024-01-01T12:00:00Z",
    "session_type": "login"
  }
}
```

### Check QR Status
**GET** `/api/v1/qr/status/{sessionId}`

Check the status of a QR authentication session.

**Response:**
```json
{
  "success": true,
  "message": "QR session status retrieved successfully",
  "data": {
    "status": "pending|scanned|approved|rejected|expired|consumed",
    "expires_at": "2024-01-01T12:00:00Z",
    "session_type": "login"
  }
}
```

### Scan QR Code
**POST** `/api/v1/qr/scan`

Scan a QR code with mobile app (requires authentication).

**Headers:**
- `Authorization`: Bearer token
- `X-Device-ID`: Device identifier

**Request Body:**
```json
{
  "qr_data": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "QR code scanned successfully",
  "data": {
    "session_id": "string",
    "session_type": "login",
    "challenge": "string",
    "nonce": "string",
    "expires_at": "2024-01-01T12:00:00Z"
  }
}
```

### Approve QR Authentication
**POST** `/api/v1/qr/approve`

Approve QR authentication from mobile app (requires authentication).

**Headers:**
- `Authorization`: Bearer token

**Request Body:**
```json
{
  "session_id": "string",
  "device_name": "string",
  "device_type": "ios|android|web|desktop",
  "identity_key": "string",
  "signed_pre_key": "string",
  "pre_key_bundle": "string",
  "registration_id": "string",
  "device_fingerprint": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "QR authentication approved successfully",
  "data": {
    "auth_token": "string",
    "session_approved": true
  }
}
```

### Reject QR Authentication
**POST** `/api/v1/qr/reject`

Reject QR authentication from mobile app (requires authentication).

**Headers:**
- `Authorization`: Bearer token

**Request Body:**
```json
{
  "session_id": "string"
}
```

## Device Linking

### Initiate Device Linking
**POST** `/api/v1/qr/device/initiate`

Initiate linking a new device (requires authentication).

**Headers:**
- `Authorization`: Bearer token
- `X-Device-ID`: Primary device identifier
- `X-Device-Fingerprint`: New device fingerprint

**Request Body:**
```json
{
  "device_name": "string",
  "device_type": "ios|android|web|desktop",
  "device_fingerprint": "string",
  "identity_key": "string",
  "signed_pre_key": "string",
  "pre_key_bundle": "string",
  "registration_id": "string",
  "app_version": "string",
  "os_version": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Device linking initiated successfully",
  "data": {
    "session_id": "string",
    "verification_code": "123456",
    "expires_at": "2024-01-01T12:00:00Z"
  }
}
```

### Verify Device Linking
**POST** `/api/v1/qr/device/verify`

Verify device linking with verification code (requires authentication).

**Headers:**
- `Authorization`: Bearer token

**Request Body:**
```json
{
  "session_id": "string",
  "verification_code": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Device linking verified successfully",
  "data": {
    "verified": true,
    "shared_secret": "string"
  }
}
```

### Complete Device Linking
**POST** `/api/v1/qr/device/complete`

Complete device linking process (requires authentication).

**Headers:**
- `Authorization`: Bearer token

**Request Body:**
```json
{
  "session_id": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Device linking completed successfully",
  "data": {
    "device_id": "string",
    "linked": true
  }
}
```

### Revoke Linked Device
**POST** `/api/v1/qr/device/revoke`

Revoke a linked device (requires authentication).

**Headers:**
- `Authorization`: Bearer token

**Request Body:**
```json
{
  "device_id": "string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Device revoked successfully",
  "data": {
    "revoked": true
  }
}
```

### List Linked Devices
**GET** `/api/v1/qr/devices/list`

List all linked devices for the authenticated user.

**Headers:**
- `Authorization`: Bearer token

**Response:**
```json
{
  "success": true,
  "message": "Linked devices retrieved successfully",
  "data": {
    "devices": [
      {
        "device_id": "string",
        "device_name": "string",
        "device_type": "ios|android|web|desktop",
        "is_primary": false,
        "is_active": true,
        "last_seen": "2024-01-01T12:00:00Z",
        "linked_at": "2024-01-01T12:00:00Z",
        "verified_at": "2024-01-01T12:00:00Z"
      }
    ],
    "total_devices": 1
  }
}
```

## Deep Links

### Generate Deep Link
**POST** `/api/v1/link/generate`

Generate deep links for mobile app integration (requires authentication).

**Headers:**
- `Authorization`: Bearer token

**Request Body:**
```json
{
  "action": "qr_scan|device_link|login_approve|device_verify",
  "session_id": "string",
  "metadata": {}
}
```

**Response:**
```json
{
  "success": true,
  "message": "Deep link generated successfully",
  "data": {
    "deep_link": "quickchat://auth/qr-scan?data=...",
    "ios_url": "quickchat://auth/qr-scan?data=...&platform=ios",
    "android_url": "quickchat://auth/qr-scan?data=...&platform=android",
    "web_fallback": "https://quickchat.app/qr-scan?session=...",
    "action": "qr_scan",
    "session_id": "string"
  }
}
```

### QR Scan Redirect
**GET** `/api/v1/link/qr-scan/{sessionId}`

Universal link handler for QR scanning.

### Device Link Redirect
**GET** `/api/v1/link/device-link/{sessionId}`

Universal link handler for device linking.

### App Store Links
**GET** `/api/v1/link/app-store`

Get app store download links.

**Response:**
```json
{
  "success": true,
  "message": "App store links retrieved successfully",
  "data": {
    "ios_app_store": "https://apps.apple.com/app/quickchat",
    "android_play_store": "https://play.google.com/store/apps/details?id=com.quickchat"
  }
}
```

## Rate Limiting

- Authentication endpoints: 5 requests per minute
- SMS endpoints: 3 requests per minute
- QR generation: 5 requests per minute
- Device linking: 3 requests per 5 minutes
- General API: 100 requests per minute
- File uploads: 10 requests per minute

## Security Features

### QR Code Security
- QR codes expire after 90-120 seconds
- Cryptographically secure random session IDs and nonces
- Device fingerprinting for additional security
- IP address validation and monitoring
- Rate limiting to prevent abuse

### Device Linking Security
- End-to-end encryption using Signal Protocol
- X3DH key agreement for secure handshake
- Curve25519 key pairs for cryptographic operations
- Maximum 5 devices per user account
- Device verification with confirmation codes
- Audit logging for all device operations

### Authentication Security
- HTTPS-only communication in production
- JWT tokens with secure expiration
- Device fingerprinting validation
- IP address monitoring and blacklisting
- User confirmation prompts for high-risk actions
- Comprehensive audit logging

### Headers Required for Security
- `X-Device-Fingerprint`: Required for QR generation and device operations
- `X-Device-ID`: Required for authenticated device operations
- `User-Agent`: Required for all requests
- `Authorization`: Bearer token for authenticated endpoints

## File Upload Limits

- Profile pictures: 5MB max, JPG/PNG only
- Status media: 10MB max, JPG/PNG/MP4 only
- Message attachments: 25MB max
- Backup files: 100MB max

## Contact Upload

- **No limit** on the number of contacts per upload
- Contacts are processed in chunks of 500 for optimal performance
- Large uploads are automatically chunked to prevent timeouts
- Progress is saved incrementally during processing

## Pagination

For endpoints that return lists, use these query parameters:
- `page`: Page number (default: 1, max: 10000)
- `limit`: Items per page (default varies by endpoint)
  - Contacts: default 100, max 500
  - Contacts (all): default 200, max 1000
  - Status: default 20, max 50
- `next`: Cursor token for cursor-based pagination

## Contact Filtering

Additional parameters for `/api/v1/contacts`:
- `search`: Search contact names (case-insensitive)
- `registered=true`: Show only registered contacts
- `all=true`: Show all contacts (registered first)

Examples:
```
GET /api/v1/contacts?page=2&limit=200
GET /api/v1/contacts?registered=true&limit=100
GET /api/v1/contacts?all=true&search=john
GET /api/v1/contacts?page=1&limit=500&all=true
```

## Postman Collection

Import the complete Postman collection from:
`/postman/QuickChat_API_Collection.json`

This collection includes:
- All request examples
- Environment variables
- Pre-request scripts for authentication
- Test scripts for response validation
```
