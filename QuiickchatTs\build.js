#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting optimized build process...');

// Set memory limits
process.env.NODE_OPTIONS = '--max-old-space-size=4096';

// Clean dist directory
console.log('🧹 Cleaning dist directory...');
if (fs.existsSync('dist')) {
  fs.rmSync('dist', { recursive: true, force: true });
}

try {
  // Run TypeScript compilation with production config
  console.log('📦 Compiling TypeScript...');
  execSync('npx tsc --project tsconfig.prod.json', { 
    stdio: 'inherit',
    env: { ...process.env, NODE_OPTIONS: '--max-old-space-size=4096' }
  });

  // Run tsc-alias to resolve path aliases
  console.log('🔗 Resolving path aliases...');
  execSync('npx tsc-alias', { 
    stdio: 'inherit',
    env: { ...process.env, NODE_OPTIONS: '--max-old-space-size=4096' }
  });

  console.log('✅ Build completed successfully!');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
