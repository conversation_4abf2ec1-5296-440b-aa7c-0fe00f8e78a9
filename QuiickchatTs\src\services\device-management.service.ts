import { User, IDeviceRecord } from '../models/user.model';
import { AuditLog } from '../models/device-link.model';
import { logger } from '../utils/logger';
import { ValidationError, NotFoundError, AuthenticationError } from '../utils/errors';
import { CryptoService } from './crypto.service';
import { v4 as uuidv4 } from 'uuid';

interface DeviceInfo {
  deviceName: string;
  deviceType: 'ios' | 'android' | 'web' | 'desktop';
  appVersion?: string;
  osVersion?: string;
  ipAddress: string;
  userAgent: string;
}

interface SecurityCheck {
  isValid: boolean;
  riskScore: number;
  reasons: string[];
}

export class DeviceManagementService {
  private static readonly MAX_DEVICES_PER_USER = 5;
  private static readonly DEVICE_INACTIVITY_DAYS = 30;
  private static readonly HIGH_RISK_THRESHOLD = 70;

  static async registerPrimaryDevice(
    userPhone: string,
    deviceInfo: DeviceInfo,
    cryptoKeys: {
      identityKey: string;
      signedPreKey: string;
      preKeyBundle: string;
      registrationId: string;
    }
  ): Promise<{ deviceId: string; success: boolean }> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const deviceFingerprint = CryptoService.generateDeviceFingerprint(
      Buffer.from(cryptoKeys.identityKey, 'base64'),
      deviceInfo
    );

    const existingPrimary = user.devices.find(d => d.is_primary);
    if (existingPrimary) {
      throw new ValidationError('Primary device already exists');
    }

    const deviceId = uuidv4();
    const primaryDevice: IDeviceRecord = {
      device_id: deviceId,
      device_name: deviceInfo.deviceName,
      device_type: deviceInfo.deviceType,
      device_fingerprint: deviceFingerprint,
      identity_key: cryptoKeys.identityKey,
      signed_pre_key: cryptoKeys.signedPreKey,
      pre_key_bundle: cryptoKeys.preKeyBundle,
      registration_id: cryptoKeys.registrationId,
      is_primary: true,
      is_active: true,
      last_seen: new Date(),
      ip_address: deviceInfo.ipAddress,
      user_agent: deviceInfo.userAgent,
      app_version: deviceInfo.appVersion,
      os_version: deviceInfo.osVersion,
      linked_at: new Date(),
      verified_at: new Date()
    };

    user.devices.push(primaryDevice);
    await user.save();

    await this.logDeviceEvent('device_registered', userPhone, deviceId, deviceInfo.ipAddress, deviceInfo.userAgent, {
      device_name: deviceInfo.deviceName,
      device_type: deviceInfo.deviceType,
      is_primary: true
    });

    logger.info(`Primary device registered for user ${userPhone}: ${deviceId}`);
    return { deviceId, success: true };
  }

  static async updateDeviceActivity(userPhone: string, deviceId: string, ipAddress: string, userAgent: string): Promise<void> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const device = user.devices.find(d => d.device_id === deviceId);
    if (!device) {
      throw new NotFoundError('Device not found');
    }

    device.last_seen = new Date();
    device.ip_address = ipAddress;
    device.user_agent = userAgent;

    await user.save();
  }

  static async performSecurityCheck(userPhone: string, deviceId: string, requestInfo: any): Promise<SecurityCheck> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const device = user.devices.find(d => d.device_id === deviceId);
    if (!device) {
      throw new NotFoundError('Device not found');
    }

    let riskScore = 0;
    const reasons: string[] = [];

    if (device.ip_address !== requestInfo.ipAddress) {
      riskScore += 20;
      reasons.push('IP address change detected');
    }

    if (device.user_agent !== requestInfo.userAgent) {
      riskScore += 15;
      reasons.push('User agent change detected');
    }

    const daysSinceLastSeen = Math.floor((Date.now() - device.last_seen.getTime()) / (1000 * 60 * 60 * 24));
    if (daysSinceLastSeen > 7) {
      riskScore += 10;
      reasons.push('Device inactive for extended period');
    }

    const recentSuspiciousActivity = await this.checkRecentSuspiciousActivity(userPhone, deviceId);
    if (recentSuspiciousActivity) {
      riskScore += 30;
      reasons.push('Recent suspicious activity detected');
    }

    if (riskScore >= this.HIGH_RISK_THRESHOLD) {
      await this.logDeviceEvent('security_violation', userPhone, deviceId, requestInfo.ipAddress, requestInfo.userAgent, {
        risk_score: riskScore,
        reasons
      });
    }

    return {
      isValid: riskScore < this.HIGH_RISK_THRESHOLD,
      riskScore,
      reasons
    };
  }

  static async getDeviceDetails(userPhone: string, deviceId: string): Promise<IDeviceRecord> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const device = user.devices.find(d => d.device_id === deviceId);
    if (!device) {
      throw new NotFoundError('Device not found');
    }

    return device;
  }

  static async updateDeviceInfo(
    userPhone: string,
    deviceId: string,
    updates: Partial<DeviceInfo>
  ): Promise<{ success: boolean }> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const device = user.devices.find(d => d.device_id === deviceId);
    if (!device) {
      throw new NotFoundError('Device not found');
    }

    if (updates.deviceName) device.device_name = updates.deviceName;
    if (updates.appVersion) device.app_version = updates.appVersion;
    if (updates.osVersion) device.os_version = updates.osVersion;
    if (updates.ipAddress) device.ip_address = updates.ipAddress;
    if (updates.userAgent) device.user_agent = updates.userAgent;

    await user.save();

    await this.logDeviceEvent('device_updated', userPhone, deviceId, updates.ipAddress || '', updates.userAgent || '', {
      updates
    });

    logger.info(`Device ${deviceId} updated for user ${userPhone}`);
    return { success: true };
  }

  static async deactivateDevice(userPhone: string, deviceId: string, reason: string): Promise<{ success: boolean }> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const device = user.devices.find(d => d.device_id === deviceId);
    if (!device) {
      throw new NotFoundError('Device not found');
    }

    if (device.is_primary) {
      throw new ValidationError('Cannot deactivate primary device');
    }

    device.is_active = false;
    await user.save();

    await this.logDeviceEvent('device_deactivated', userPhone, deviceId, '', '', {
      reason
    });

    logger.info(`Device ${deviceId} deactivated for user ${userPhone}: ${reason}`);
    return { success: true };
  }

  static async cleanupInactiveDevices(userPhone: string): Promise<{ removedCount: number }> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const cutoffDate = new Date(Date.now() - this.DEVICE_INACTIVITY_DAYS * 24 * 60 * 60 * 1000);
    const inactiveDevices = user.devices.filter(
      d => !d.is_primary && d.last_seen < cutoffDate
    );

    for (const device of inactiveDevices) {
      await this.logDeviceEvent('device_auto_removed', userPhone, device.device_id, '', '', {
        reason: 'Inactive for over 30 days',
        last_seen: device.last_seen
      });
    }

    user.devices = user.devices.filter(d => d.is_primary || d.last_seen >= cutoffDate);
    await user.save();

    logger.info(`Cleaned up ${inactiveDevices.length} inactive devices for user ${userPhone}`);
    return { removedCount: inactiveDevices.length };
  }

  static async getDeviceStatistics(userPhone: string): Promise<{
    totalDevices: number;
    activeDevices: number;
    primaryDevice: IDeviceRecord | null;
    lastActivity: Date | null;
    deviceTypes: Record<string, number>;
  }> {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const activeDevices = user.devices.filter(d => d.is_active);
    const primaryDevice = user.devices.find(d => d.is_primary) || null;
    const lastActivity = activeDevices.length > 0 
      ? new Date(Math.max(...activeDevices.map(d => d.last_seen.getTime())))
      : null;

    const deviceTypes = activeDevices.reduce((acc, device) => {
      acc[device.device_type] = (acc[device.device_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalDevices: user.devices.length,
      activeDevices: activeDevices.length,
      primaryDevice,
      lastActivity,
      deviceTypes
    };
  }

  static async validateDeviceFingerprint(
    userPhone: string,
    deviceId: string,
    providedFingerprint: string
  ): Promise<{ isValid: boolean }> {
    const device = await this.getDeviceDetails(userPhone, deviceId);
    
    const isValid = CryptoService.constantTimeCompare(
      Buffer.from(device.device_fingerprint, 'hex'),
      Buffer.from(providedFingerprint, 'hex')
    );

    if (!isValid) {
      await this.logDeviceEvent('fingerprint_mismatch', userPhone, deviceId, '', '', {
        expected: device.device_fingerprint,
        provided: providedFingerprint
      });
    }

    return { isValid };
  }

  private static async checkRecentSuspiciousActivity(userPhone: string, deviceId: string): Promise<boolean> {
    const recentDate = new Date(Date.now() - 24 * 60 * 60 * 1000); // Last 24 hours
    
    const suspiciousEvents = await AuditLog.countDocuments({
      user_phone: userPhone,
      device_id: deviceId,
      event_type: { $in: ['security_violation', 'fingerprint_mismatch', 'unauthorized_access'] },
      created_at: { $gte: recentDate }
    });

    return suspiciousEvents > 0;
  }

  private static async logDeviceEvent(
    eventType: string,
    userPhone: string,
    deviceId: string,
    ipAddress: string,
    userAgent: string,
    eventData: any
  ): Promise<void> {
    try {
      const auditLog = new AuditLog({
        event_type: eventType,
        user_phone: userPhone,
        device_id: deviceId,
        ip_address: ipAddress,
        user_agent: userAgent,
        event_data: eventData
      });
      await auditLog.save();
    } catch (error) {
      logger.error('Failed to log device event:', error);
    }
  }
}
