#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to calculate relative path
function getRelativePath(fromFile, toFile) {
  const fromDir = path.dirname(fromFile);
  const relativePath = path.relative(fromDir, toFile);
  
  // Ensure the path starts with ./ or ../
  if (!relativePath.startsWith('.')) {
    return './' + relativePath;
  }
  return relativePath;
}

// Function to convert @/ imports to relative imports
function convertImports(filePath, content) {
  const srcDir = path.join(__dirname, '../src');

  // Use regex to replace all @/ imports, including multiline ones
  const updatedContent = content.replace(
    /(['"])@\/([^'"]+)(['"])/g,
    (match, quote1, importPath, quote2) => {
      // Calculate the target file path
      const targetPath = path.join(srcDir, importPath);

      // Calculate relative path from current file to target
      const relativePath = getRelativePath(filePath, targetPath);

      // Convert backslashes to forward slashes for consistency
      const normalizedPath = relativePath.replace(/\\/g, '/');

      return `${quote1}${normalizedPath}${quote2}`;
    }
  );

  return updatedContent;
}

// Function to recursively find all TypeScript files
function findTsFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && item !== 'node_modules' && item !== 'dist') {
      files.push(...findTsFiles(fullPath));
    } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Main function
function fixImports() {
  console.log('🔧 Fixing @/ imports to relative imports...');
  
  const srcDir = path.join(__dirname, '../src');
  const tsFiles = findTsFiles(srcDir);
  
  let filesModified = 0;
  
  for (const filePath of tsFiles) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if file has @/ imports
    if (content.includes('@/')) {
      const updatedContent = convertImports(filePath, content);
      
      if (updatedContent !== content) {
        fs.writeFileSync(filePath, updatedContent, 'utf8');
        console.log(`✅ Fixed imports in: ${path.relative(process.cwd(), filePath)}`);
        filesModified++;
      }
    }
  }
  
  console.log(`\n🎉 Fixed imports in ${filesModified} files!`);
}

// Run the script
fixImports();
