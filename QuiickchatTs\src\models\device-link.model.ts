import mongoose, { Document, Schema } from 'mongoose';

export interface IDeviceLinkSession extends Document {
  session_id: string;
  user_phone: string;
  primary_device_id: string;
  linking_device_data: {
    device_name: string;
    device_type: 'ios' | 'android' | 'web' | 'desktop';
    device_fingerprint: string;
    identity_key: string;
    signed_pre_key: string;
    pre_key_bundle: string;
    registration_id: string;
    ip_address: string;
    user_agent: string;
    app_version?: string;
    os_version?: string;
  };
  handshake_data: {
    x3dh_bundle: string;
    ephemeral_key: string;
    shared_secret?: string;
    root_key?: string;
    chain_key?: string;
  };
  verification_data: {
    challenge: string;
    response?: string;
    verification_code: string;
    attempts: number;
  };
  status: 'pending' | 'verified' | 'completed' | 'failed' | 'expired';
  expires_at: Date;
  verified_at?: Date;
  completed_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface IAuditLog extends Document {
  event_type: 'qr_generated' | 'qr_scanned' | 'device_linked' | 'device_revoked' | 'login_attempt' | 'security_violation';
  user_phone?: string;
  device_id?: string;
  session_id?: string;
  ip_address: string;
  user_agent: string;
  event_data: Record<string, any>;
  risk_score?: number;
  created_at: Date;
}

const DeviceLinkSessionSchema = new Schema<IDeviceLinkSession>({
  session_id: { type: String, required: true, unique: true, index: true },
  user_phone: { type: String, required: true, index: true },
  primary_device_id: { type: String, required: true },
  linking_device_data: {
    device_name: { type: String, required: true },
    device_type: { type: String, enum: ['ios', 'android', 'web', 'desktop'], required: true },
    device_fingerprint: { type: String, required: true },
    identity_key: { type: String, required: true },
    signed_pre_key: { type: String, required: true },
    pre_key_bundle: { type: String, required: true },
    registration_id: { type: String, required: true },
    ip_address: { type: String, required: true },
    user_agent: { type: String, required: true },
    app_version: { type: String },
    os_version: { type: String }
  },
  handshake_data: {
    x3dh_bundle: { type: String, required: true },
    ephemeral_key: { type: String, required: true },
    shared_secret: { type: String },
    root_key: { type: String },
    chain_key: { type: String }
  },
  verification_data: {
    challenge: { type: String, required: true },
    response: { type: String },
    verification_code: { type: String, required: true },
    attempts: { type: Number, default: 0 }
  },
  status: { 
    type: String, 
    enum: ['pending', 'verified', 'completed', 'failed', 'expired'], 
    default: 'pending',
    index: true
  },
  expires_at: { type: Date, required: true },
  verified_at: { type: Date },
  completed_at: { type: Date },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

const AuditLogSchema = new Schema<IAuditLog>({
  event_type: { 
    type: String, 
    enum: ['qr_generated', 'qr_scanned', 'device_linked', 'device_revoked', 'login_attempt', 'security_violation'],
    required: true,
    index: true
  },
  user_phone: { type: String, index: true },
  device_id: { type: String, index: true },
  session_id: { type: String, index: true },
  ip_address: { type: String, required: true },
  user_agent: { type: String, required: true },
  event_data: { type: Schema.Types.Mixed, required: true },
  risk_score: { type: Number, min: 0, max: 100 },
  created_at: { type: Date, default: Date.now, index: true }
});

DeviceLinkSessionSchema.index({ expires_at: 1 }, { expireAfterSeconds: 0 });
DeviceLinkSessionSchema.index({ user_phone: 1, status: 1 });
DeviceLinkSessionSchema.index({ status: 1, created_at: -1 });

AuditLogSchema.index({ created_at: -1 });
AuditLogSchema.index({ event_type: 1, created_at: -1 });
AuditLogSchema.index({ user_phone: 1, created_at: -1 });

export const DeviceLinkSession = mongoose.model<IDeviceLinkSession>('DeviceLinkSession', DeviceLinkSessionSchema);
export const AuditLog = mongoose.model<IAuditLog>('AuditLog', AuditLogSchema);
