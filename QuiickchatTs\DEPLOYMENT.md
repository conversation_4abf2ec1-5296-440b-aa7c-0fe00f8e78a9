# Deployment Guide

## Memory Optimization for Render

This project has been optimized to handle JavaScript heap memory issues during build processes on platforms like Render.

### Build Optimizations Implemented

1. **Memory-Optimized Build Scripts**
   - `npm run build:prod` - Production build with memory optimization
   - `npm run build:render` - Render-specific build command
   - Uses `--max-old-space-size=4096` to allocate 4GB heap memory

2. **Production TypeScript Configuration**
   - `tsconfig.prod.json` - Optimized for production builds
   - Disabled source maps, declarations, and declaration maps
   - Reduced memory footprint during compilation

3. **Jest Configuration Optimization**
   - Limited to single worker (`maxWorkers: 1`)
   - Memory limit for workers (`workerIdleMemoryLimit: '512MB'`)
   - Isolated modules for faster compilation

### Render Deployment

#### Option 1: Using render.yaml (Recommended)
The project includes a `render.yaml` file with optimized settings:
- Memory allocation: 4GB heap space
- Production build command: `npm run build:prod`
- Environment variables for memory optimization

#### Option 2: Manual Configuration
If not using `render.yaml`, configure your Render service with:

**Build Command:**
```bash
npm run build:render
```

**Start Command:**
```bash
npm start
```

**Environment Variables:**
```
NODE_ENV=production
NODE_OPTIONS=--max-old-space-size=4096
```

### Memory Requirements

- **Minimum RAM**: 2GB
- **Recommended RAM**: 4GB or higher
- **Build Memory**: 4GB heap space allocated

### Troubleshooting Memory Issues

If you still encounter memory issues:

1. **Increase Memory Allocation**
   ```bash
   NODE_OPTIONS=--max-old-space-size=8192 npm run build:prod
   ```

2. **Use Render's Higher Plans**
   - Upgrade to a plan with more memory (4GB+ recommended)

3. **Optimize Dependencies**
   ```bash
   npm audit
   npm update
   ```

4. **Clear Build Cache**
   ```bash
   rm -rf node_modules dist
   npm install
   npm run build:prod
   ```

### Performance Tips

1. **Use Production Build**: Always use `npm run build:prod` for deployments
2. **Node.js Version**: Use Node.js 18.x (specified in `.nvmrc`)
3. **Clean Builds**: Remove `dist` folder before building
4. **Monitor Memory**: Check Render logs for memory usage patterns

### Local Development

For local development with memory constraints:
```bash
NODE_OPTIONS=--max-old-space-size=2048 npm run dev
```

### Build Process Details

The optimized build process:
1. Cleans the `dist` directory
2. Compiles TypeScript with production config
3. Resolves path aliases with `tsc-alias`
4. Uses memory-optimized Node.js settings throughout

This ensures reliable builds even on memory-constrained environments like Render's free tier.
