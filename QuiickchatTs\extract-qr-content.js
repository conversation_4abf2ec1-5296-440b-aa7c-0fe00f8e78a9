#!/usr/bin/env node

/**
 * QuickChat QR Content Extractor
 * 
 * This script helps extract and analyze the actual content from your QR code.
 * If you can scan the QR code with any QR reader app, paste the content here.
 */

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(level, message) {
  const colorMap = {
    info: colors.cyan,
    success: colors.green,
    warn: colors.yellow,
    error: colors.red
  };
  const color = colorMap[level] || colors.reset;
  console.log(`${color}${message}${colors.reset}`);
}

console.log(`${colors.bright}${colors.blue}📱 QuickChat QR Content Extractor${colors.reset}`);
console.log(`${colors.cyan}Extract and analyze QR code content${colors.reset}\n`);

log('info', '🔍 To analyze your QR code content:');
log('info', '');
log('info', '1. 📱 Use any QR code scanner app on your phone');
log('info', '2. 📷 Scan the QR code image you provided');
log('info', '3. 📋 Copy the text content that appears');
log('info', '4. 🔧 Run: node analyze-qr-data.js "PASTE_CONTENT_HERE"');
log('info', '');

log('info', '📋 Expected QR content format:');
log('success', '{"v":"1.0","t":"login","s":"session-xyz","c":"challenge-abc","n":"nonce-123","e":1234567890}');
log('info', '');

log('info', '🛠️ Alternative methods:');
log('info', '• Online QR decoder: https://zxing.org/w/decode.jsp');
log('info', '• Upload your QR image and copy the result');
log('info', '• Then run: node analyze-qr-data.js "RESULT"');
log('info', '');

log('info', '📚 What the QR code contains:');
log('info', '• v = Protocol version (1.0)');
log('info', '• t = Type (login or device_link)');
log('info', '• s = Session ID (unique identifier)');
log('info', '• c = Challenge (security verification)');
log('info', '• n = Nonce (prevents replay attacks)');
log('info', '• e = Expiry (Unix timestamp)');
log('info', '');

log('success', '🎯 Once you have the content, run:');
log('success', 'node analyze-qr-data.js "YOUR_QR_CONTENT"');

// If content is provided as argument
if (process.argv[2]) {
  log('info', '\n🔍 Analyzing provided content...');
  const { spawn } = require('child_process');
  const child = spawn('node', ['analyze-qr-data.js', process.argv[2]], { stdio: 'inherit' });
}
