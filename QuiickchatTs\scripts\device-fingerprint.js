#!/usr/bin/env node

/**
 * QuickChat QR Authentication & Device Fingerprinting Test Suite
 *
 * Usage:
 *   node device-fingerprint.js                    # Run full test suite
 *   node device-fingerprint.js fingerprint        # Generate and log fingerprint only
 *   node device-fingerprint.js qr-login          # Test QR login generation
 *   node device-fingerprint.js qr-device-link    # Test QR device linking
 *   node device-fingerprint.js device-info       # Show detailed device info
 *   node device-fingerprint.js mock-requests     # Generate mock HTTP requests
 *   node device-fingerprint.js security-test     # Test security features
 *
 * Environment Variables:
 *   API_BASE_URL=http://localhost:3000/api/v1     # API base URL
 *   LOG_LEVEL=debug                               # Logging level
 *   MOCK_AUTH_TOKEN=your_token_here              # Mock auth token for testing
 */

const crypto = require('crypto');
const os = require('os');
const fs = require('fs');
const path = require('path');
const { URL } = require('url');

// ANSI color codes for better console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

class QuickChatQRTester {
  constructor() {
    this.baseUrl = process.env.API_BASE_URL || 'http://localhost:3000/api/v1';
    this.logLevel = process.env.LOG_LEVEL || 'info';
    this.mockAuthToken = process.env.MOCK_AUTH_TOKEN || this.generateMockAuthToken();

    // Collect comprehensive device information
    this.deviceInfo = this.collectDetailedDeviceInfo();
    this.fingerprint = this.generateSecureFingerprint();
    this.sessionData = {};

    // Test counters
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0
    };

    this.log('info', 'QuickChat QR Authentication Tester initialized');
    this.log('debug', `Base URL: ${this.baseUrl}`);
    this.log('debug', `Log Level: ${this.logLevel}`);
  }

  // Logging utility with colors and levels
  log(level, message, data = null) {
    const levels = { debug: 0, info: 1, warn: 2, error: 3 };
    const currentLevel = levels[this.logLevel] || 1;

    if (levels[level] < currentLevel) return;

    const timestamp = new Date().toISOString();
    const colorMap = {
      debug: colors.cyan,
      info: colors.green,
      warn: colors.yellow,
      error: colors.red
    };

    const color = colorMap[level] || colors.white;
    console.log(`${color}[${timestamp}] ${level.toUpperCase()}: ${message}${colors.reset}`);

    if (data) {
      console.log(`${colors.bright}${JSON.stringify(data, null, 2)}${colors.reset}`);
    }
  }

  collectDetailedDeviceInfo() {
    this.log('debug', 'Collecting detailed device information...');

    const networkInterfaces = os.networkInterfaces();
    const macAddresses = [];
    const ipAddresses = [];

    // Collect network information
    for (const interfaceName in networkInterfaces) {
      const interfaces = networkInterfaces[interfaceName];
      for (const iface of interfaces) {
        if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
          macAddresses.push({
            interface: interfaceName,
            mac: iface.mac,
            family: iface.family,
            address: iface.address,
            internal: iface.internal
          });
        }
        if (!iface.internal) {
          ipAddresses.push({
            interface: interfaceName,
            family: iface.family,
            address: iface.address
          });
        }
      }
    }

    // Collect CPU information
    const cpus = os.cpus();
    const cpuInfo = {
      count: cpus.length,
      model: cpus[0]?.model || 'Unknown',
      speed: cpus[0]?.speed || 0,
      architecture: os.arch()
    };

    // Collect memory information
    const memInfo = {
      total: os.totalmem(),
      free: os.freemem(),
      used: os.totalmem() - os.freemem(),
      usagePercent: Math.round(((os.totalmem() - os.freemem()) / os.totalmem()) * 100)
    };

    // Collect system information
    const systemInfo = {
      platform: os.platform(),
      release: os.release(),
      type: os.type(),
      hostname: os.hostname(),
      uptime: os.uptime(),
      loadavg: os.loadavg(),
      endianness: os.endianness()
    };

    // Collect process information
    const processInfo = {
      nodeVersion: process.version,
      pid: process.pid,
      ppid: process.ppid,
      platform: process.platform,
      arch: process.arch,
      execPath: process.execPath,
      argv: process.argv,
      env: {
        NODE_ENV: process.env.NODE_ENV,
        PATH: process.env.PATH?.substring(0, 100) + '...',
        USER: process.env.USER || process.env.USERNAME,
        HOME: process.env.HOME || process.env.USERPROFILE
      }
    };

    // Browser-like information (mocked for Node.js)
    const browserInfo = {
      userAgent: 'QuickChat-Test-Client/1.0.0 (Node.js)',
      language: process.env.LANG || 'en-US',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      screenResolution: '1920x1080',
      colorDepth: 24,
      pixelRatio: 1,
      cookieEnabled: true,
      doNotTrack: false
    };

    const deviceInfo = {
      system: systemInfo,
      cpu: cpuInfo,
      memory: memInfo,
      network: {
        macAddresses,
        ipAddresses,
        hostname: os.hostname()
      },
      process: processInfo,
      browser: browserInfo,
      timestamp: Date.now(),
      iso_timestamp: new Date().toISOString()
    };

    this.log('debug', 'Device information collected', {
      macCount: macAddresses.length,
      ipCount: ipAddresses.length,
      cpuCount: cpuInfo.count,
      memoryGB: Math.round(memInfo.total / 1024 / 1024 / 1024)
    });

    return deviceInfo;
  }

  generateSecureFingerprint() {
    this.log('debug', 'Generating secure device fingerprint...');

    // Create a comprehensive fingerprint using multiple data points
    const fingerprintData = {
      // System identifiers
      platform: this.deviceInfo.system.platform,
      arch: this.deviceInfo.cpu.architecture,
      hostname: this.deviceInfo.system.hostname,

      // Hardware identifiers
      cpuCount: this.deviceInfo.cpu.count,
      cpuModel: this.deviceInfo.cpu.model,
      totalMemory: this.deviceInfo.memory.total,

      // Network identifiers (sorted for consistency)
      macAddresses: this.deviceInfo.network.macAddresses
        .map(mac => mac.mac)
        .sort(),

      // Software identifiers
      nodeVersion: this.deviceInfo.process.nodeVersion,
      userAgent: this.deviceInfo.browser.userAgent,
      timezone: this.deviceInfo.browser.timezone,
      language: this.deviceInfo.browser.language,

      // Display identifiers
      screenResolution: this.deviceInfo.browser.screenResolution,
      colorDepth: this.deviceInfo.browser.colorDepth,
      pixelRatio: this.deviceInfo.browser.pixelRatio
    };

    // Create multiple hash layers for security
    const primaryHash = crypto.createHash('sha256')
      .update(JSON.stringify(fingerprintData))
      .digest('hex');

    const secondaryHash = crypto.createHash('sha512')
      .update(primaryHash + this.deviceInfo.timestamp)
      .digest('hex');

    const finalFingerprint = crypto.createHash('sha256')
      .update(secondaryHash)
      .digest('hex');

    this.log('debug', 'Fingerprint generated', {
      primaryHashLength: primaryHash.length,
      secondaryHashLength: secondaryHash.length,
      finalFingerprintLength: finalFingerprint.length,
      macAddressCount: this.deviceInfo.network.macAddresses.length
    });

    return finalFingerprint;
  }

  logDetailedDeviceInfo() {
    console.log(`\n${colors.bright}${colors.blue}=== COMPREHENSIVE DEVICE INFORMATION ===${colors.reset}`);

    // System Information
    console.log(`\n${colors.yellow}📱 SYSTEM INFORMATION:${colors.reset}`);
    console.log(`  Platform: ${this.deviceInfo.system.platform}`);
    console.log(`  Type: ${this.deviceInfo.system.type}`);
    console.log(`  Release: ${this.deviceInfo.system.release}`);
    console.log(`  Architecture: ${this.deviceInfo.cpu.architecture}`);
    console.log(`  Hostname: ${this.deviceInfo.system.hostname}`);
    console.log(`  Uptime: ${Math.round(this.deviceInfo.system.uptime / 3600)} hours`);
    console.log(`  Endianness: ${this.deviceInfo.system.endianness}`);

    // CPU Information
    console.log(`\n${colors.yellow}🖥️  CPU INFORMATION:${colors.reset}`);
    console.log(`  Count: ${this.deviceInfo.cpu.count} cores`);
    console.log(`  Model: ${this.deviceInfo.cpu.model}`);
    console.log(`  Speed: ${this.deviceInfo.cpu.speed} MHz`);
    console.log(`  Load Average: [${this.deviceInfo.system.loadavg.map(l => l.toFixed(2)).join(', ')}]`);

    // Memory Information
    console.log(`\n${colors.yellow}💾 MEMORY INFORMATION:${colors.reset}`);
    console.log(`  Total: ${Math.round(this.deviceInfo.memory.total / 1024 / 1024 / 1024)} GB`);
    console.log(`  Free: ${Math.round(this.deviceInfo.memory.free / 1024 / 1024 / 1024)} GB`);
    console.log(`  Used: ${Math.round(this.deviceInfo.memory.used / 1024 / 1024 / 1024)} GB`);
    console.log(`  Usage: ${this.deviceInfo.memory.usagePercent}%`);

    // Network Information
    console.log(`\n${colors.yellow}🌐 NETWORK INFORMATION:${colors.reset}`);
    console.log(`  MAC Addresses (${this.deviceInfo.network.macAddresses.length}):`);
    this.deviceInfo.network.macAddresses.forEach((mac, i) => {
      console.log(`    ${i + 1}. ${mac.interface}: ${mac.mac} (${mac.family})`);
    });
    console.log(`  IP Addresses (${this.deviceInfo.network.ipAddresses.length}):`);
    this.deviceInfo.network.ipAddresses.forEach((ip, i) => {
      console.log(`    ${i + 1}. ${ip.interface}: ${ip.address} (${ip.family})`);
    });

    // Process Information
    console.log(`\n${colors.yellow}⚙️  PROCESS INFORMATION:${colors.reset}`);
    console.log(`  Node Version: ${this.deviceInfo.process.nodeVersion}`);
    console.log(`  PID: ${this.deviceInfo.process.pid}`);
    console.log(`  PPID: ${this.deviceInfo.process.ppid}`);
    console.log(`  Exec Path: ${this.deviceInfo.process.execPath}`);
    console.log(`  User: ${this.deviceInfo.process.env.USER}`);
    console.log(`  Home: ${this.deviceInfo.process.env.HOME}`);

    // Browser Information
    console.log(`\n${colors.yellow}🌍 BROWSER-LIKE INFORMATION:${colors.reset}`);
    console.log(`  User Agent: ${this.deviceInfo.browser.userAgent}`);
    console.log(`  Language: ${this.deviceInfo.browser.language}`);
    console.log(`  Timezone: ${this.deviceInfo.browser.timezone}`);
    console.log(`  Screen Resolution: ${this.deviceInfo.browser.screenResolution}`);
    console.log(`  Color Depth: ${this.deviceInfo.browser.colorDepth} bits`);
    console.log(`  Pixel Ratio: ${this.deviceInfo.browser.pixelRatio}`);

    // Fingerprint Information
    console.log(`\n${colors.bright}${colors.green}🔐 DEVICE FINGERPRINT:${colors.reset}`);
    console.log(`  Fingerprint: ${colors.cyan}${this.fingerprint}${colors.reset}`);
    console.log(`  Length: ${this.fingerprint.length} characters`);
    console.log(`  Algorithm: SHA-256 (multi-layer)`);
    console.log(`  Generated At: ${this.deviceInfo.iso_timestamp}`);
    console.log(`  Entropy Sources: ${this.deviceInfo.network.macAddresses.length + 8} unique identifiers`);
  }

  // Generate mock auth token for testing
  generateMockAuthToken() {
    const header = Buffer.from(JSON.stringify({
      alg: 'HS256',
      typ: 'JWT'
    })).toString('base64url');

    const payload = Buffer.from(JSON.stringify({
      phone: '+1234567890',
      userId: 'test-user-' + Date.now(),
      deviceId: 'test-device-' + Date.now(),
      exp: Math.floor(Date.now() / 1000) + 3600,
      iat: Math.floor(Date.now() / 1000),
      iss: 'quickchat-test'
    })).toString('base64url');

    const signature = crypto.randomBytes(32).toString('base64url');

    return `${header}.${payload}.${signature}`;
  }

  // Test result tracking
  recordTestResult(testName, passed, details = null) {
    this.testResults.total++;
    if (passed) {
      this.testResults.passed++;
      this.log('info', `✅ ${testName} - PASSED`, details);
    } else {
      this.testResults.failed++;
      this.log('error', `❌ ${testName} - FAILED`, details);
    }
  }

  // Generate detailed QR request payload and cURL commands
  generateQRRequestDetails(sessionType = 'login') {
    this.log('info', `Generating QR request details for ${sessionType.toUpperCase()}`);

    const payload = {
      session_type: sessionType,
      device_fingerprint: this.fingerprint
    };

    const headers = {
      'Content-Type': 'application/json',
      'User-Agent': this.deviceInfo.browser.userAgent,
      'X-Device-Fingerprint': this.fingerprint,
      'Accept': 'application/json',
      'Cache-Control': 'no-cache'
    };

    console.log(`\n${colors.bright}${colors.blue}=== QR ${sessionType.toUpperCase()} REQUEST DETAILS ===${colors.reset}`);

    console.log(`\n${colors.yellow}📋 REQUEST PAYLOAD:${colors.reset}`);
    console.log(JSON.stringify(payload, null, 2));

    console.log(`\n${colors.yellow}📨 REQUEST HEADERS:${colors.reset}`);
    Object.entries(headers).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });

    console.log(`\n${colors.yellow}🌐 ENDPOINT DETAILS:${colors.reset}`);
    console.log(`  Method: POST`);
    console.log(`  URL: ${this.baseUrl}/qr/generate`);
    console.log(`  Content-Type: application/json`);
    console.log(`  Expected Status: 201 Created`);

    console.log(`\n${colors.yellow}💻 CURL COMMAND:${colors.reset}`);
    console.log(`${colors.cyan}curl -X POST "${this.baseUrl}/qr/generate" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "User-Agent: ${this.deviceInfo.browser.userAgent}" \\`);
    console.log(`  -H "X-Device-Fingerprint: ${this.fingerprint}" \\`);
    console.log(`  -H "Accept: application/json" \\`);
    console.log(`  -H "Cache-Control: no-cache" \\`);
    console.log(`  -d '${JSON.stringify(payload)}'${colors.reset}`);

    console.log(`\n${colors.yellow}🔧 JAVASCRIPT FETCH:${colors.reset}`);
    console.log(`${colors.cyan}fetch('${this.baseUrl}/qr/generate', {`);
    console.log(`  method: 'POST',`);
    console.log(`  headers: ${JSON.stringify(headers, null, 4)},`);
    console.log(`  body: JSON.stringify(${JSON.stringify(payload)})`);
    console.log(`}).then(response => response.json()).then(data => console.log(data));${colors.reset}`);

    return { payload, headers, endpoint: `${this.baseUrl}/qr/generate` };
  }

  // Generate QR status check request details
  generateQRStatusRequestDetails(sessionId) {
    console.log(`\n${colors.bright}${colors.blue}=== QR STATUS CHECK REQUEST DETAILS ===${colors.reset}`);

    const endpoint = `${this.baseUrl}/qr/status/${sessionId}`;
    const headers = {
      'User-Agent': this.deviceInfo.browser.userAgent,
      'Accept': 'application/json',
      'Cache-Control': 'no-cache'
    };

    console.log(`\n${colors.yellow}🌐 ENDPOINT DETAILS:${colors.reset}`);
    console.log(`  Method: GET`);
    console.log(`  URL: ${endpoint}`);
    console.log(`  Session ID: ${sessionId}`);
    console.log(`  Expected Status: 200 OK`);

    console.log(`\n${colors.yellow}📨 REQUEST HEADERS:${colors.reset}`);
    Object.entries(headers).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });

    console.log(`\n${colors.yellow}💻 CURL COMMAND:${colors.reset}`);
    console.log(`${colors.cyan}curl -X GET "${endpoint}" \\`);
    console.log(`  -H "User-Agent: ${this.deviceInfo.browser.userAgent}" \\`);
    console.log(`  -H "Accept: application/json" \\`);
    console.log(`  -H "Cache-Control: no-cache"${colors.reset}`);

    console.log(`\n${colors.yellow}🔧 JAVASCRIPT FETCH:${colors.reset}`);
    console.log(`${colors.cyan}fetch('${endpoint}', {`);
    console.log(`  method: 'GET',`);
    console.log(`  headers: ${JSON.stringify(headers, null, 4)}`);
    console.log(`}).then(response => response.json()).then(data => console.log(data));${colors.reset}`);

    return { endpoint, headers, sessionId };
  }

  // Generate QR scan request details
  generateQRScanRequestDetails(qrData, authToken) {
    console.log(`\n${colors.bright}${colors.blue}=== QR SCAN REQUEST DETAILS ===${colors.reset}`);

    const payload = {
      qr_data: qrData
    };

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authToken}`,
      'User-Agent': this.deviceInfo.browser.userAgent,
      'X-Device-ID': 'mobile-device-' + Date.now(),
      'Accept': 'application/json'
    };

    const endpoint = `${this.baseUrl}/qr/scan`;

    console.log(`\n${colors.yellow}📋 REQUEST PAYLOAD:${colors.reset}`);
    console.log(JSON.stringify(payload, null, 2));

    console.log(`\n${colors.yellow}📨 REQUEST HEADERS:${colors.reset}`);
    Object.entries(headers).forEach(([key, value]) => {
      const displayValue = key === 'Authorization' ? value.substring(0, 20) + '...' : value;
      console.log(`  ${key}: ${displayValue}`);
    });

    console.log(`\n${colors.yellow}🌐 ENDPOINT DETAILS:${colors.reset}`);
    console.log(`  Method: POST`);
    console.log(`  URL: ${endpoint}`);
    console.log(`  Auth Required: Yes`);
    console.log(`  Expected Status: 200 OK`);

    console.log(`\n${colors.yellow}💻 CURL COMMAND:${colors.reset}`);
    console.log(`${colors.cyan}curl -X POST "${endpoint}" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "Authorization: Bearer ${authToken.substring(0, 20)}..." \\`);
    console.log(`  -H "User-Agent: ${this.deviceInfo.browser.userAgent}" \\`);
    console.log(`  -H "X-Device-ID: ${headers['X-Device-ID']}" \\`);
    console.log(`  -d '${JSON.stringify(payload)}'${colors.reset}`);

    return { payload, headers, endpoint };
  }

  async testRateLimit() {
    console.log('\n=== TESTING RATE LIMITING ===');
    
    const requests = [];
    const maxRequests = 7; // Should hit rate limit after 5

    for (let i = 1; i <= maxRequests; i++) {
      console.log(`Making request ${i}/${maxRequests}...`);
      
      try {
        const response = await axios.post(`${this.baseUrl}/qr/generate`, {
          session_type: 'login',
          device_fingerprint: this.fingerprint
        }, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': this.deviceInfo.userAgent,
            'X-Device-Fingerprint': this.fingerprint
          }
        });

        console.log(`✅ Request ${i}: Success (${response.status})`);
        requests.push({ request: i, success: true, status: response.status });
      } catch (error) {
        console.log(`❌ Request ${i}: Failed (${error.response?.status}) - ${error.response?.data?.message}`);
        requests.push({ 
          request: i, 
          success: false, 
          status: error.response?.status,
          message: error.response?.data?.message 
        });
      }

      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('\n=== RATE LIMIT TEST RESULTS ===');
    requests.forEach(req => {
      const status = req.success ? '✅' : '❌';
      console.log(`${status} Request ${req.request}: ${req.success ? 'Success' : 'Failed'} (${req.status})`);
      if (req.message) {
        console.log(`   Message: ${req.message}`);
      }
    });

    return requests;
  }

  async testDeviceLinking(authToken) {
    console.log('\n=== TESTING DEVICE LINKING ===');
    
    if (!authToken) {
      console.log('❌ No auth token provided, skipping device linking test');
      return null;
    }

    try {
      const response = await axios.post(`${this.baseUrl}/qr/device/initiate`, {
        device_name: `Test Device ${Date.now()}`,
        device_type: 'desktop',
        device_fingerprint: this.fingerprint,
        identity_key: crypto.randomBytes(32).toString('base64'),
        signed_pre_key: crypto.randomBytes(32).toString('base64'),
        pre_key_bundle: crypto.randomBytes(64).toString('base64'),
        registration_id: crypto.randomBytes(16).toString('hex'),
        app_version: '1.0.0',
        os_version: this.deviceInfo.platform
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
          'User-Agent': this.deviceInfo.userAgent,
          'X-Device-ID': 'primary-device-' + Date.now(),
          'X-Device-Fingerprint': this.fingerprint
        }
      });

      console.log('✅ Device Linking Initiation Success');
      console.log('Session ID:', response.data.data.session_id);
      console.log('Verification Code:', response.data.data.verification_code);
      console.log('Expires At:', response.data.data.expires_at);

      return response.data.data;
    } catch (error) {
      console.log('❌ Device Linking Failed');
      console.log('Error:', error.response?.data?.message || error.message);
      console.log('Status:', error.response?.status);
      return null;
    }
  }

  async testAppStoreLinks() {
    console.log('\n=== TESTING APP STORE LINKS ===');
    
    try {
      const response = await axios.get(`${this.baseUrl}/link/app-store`);

      console.log('✅ App Store Links Success');
      console.log('iOS App Store:', response.data.data.ios_app_store);
      console.log('Android Play Store:', response.data.data.android_play_store);

      return response.data.data;
    } catch (error) {
      console.log('❌ App Store Links Failed');
      console.log('Error:', error.response?.data?.message || error.message);
      return null;
    }
  }

  generateMockAuthToken() {
    // Generate a mock JWT-like token for testing
    const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64');
    const payload = Buffer.from(JSON.stringify({ 
      phone: '+1234567890',
      userId: 'test-user-' + Date.now(),
      exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour
    })).toString('base64');
    const signature = crypto.randomBytes(32).toString('base64');
    
    return `${header}.${payload}.${signature}`;
  }

  // Generate device linking request details
  generateDeviceLinkRequestDetails(authToken) {
    console.log(`\n${colors.bright}${colors.blue}=== DEVICE LINKING REQUEST DETAILS ===${colors.reset}`);

    const payload = {
      device_name: `Test Device ${Date.now()}`,
      device_type: 'desktop',
      device_fingerprint: this.fingerprint,
      identity_key: crypto.randomBytes(32).toString('base64'),
      signed_pre_key: crypto.randomBytes(32).toString('base64'),
      pre_key_bundle: crypto.randomBytes(64).toString('base64'),
      registration_id: crypto.randomBytes(16).toString('hex'),
      app_version: '1.0.0',
      os_version: this.deviceInfo.system.platform
    };

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authToken}`,
      'User-Agent': this.deviceInfo.browser.userAgent,
      'X-Device-ID': 'primary-device-' + Date.now(),
      'X-Device-Fingerprint': this.fingerprint,
      'Accept': 'application/json'
    };

    const endpoint = `${this.baseUrl}/qr/device/initiate`;

    console.log(`\n${colors.yellow}📋 REQUEST PAYLOAD:${colors.reset}`);
    console.log(JSON.stringify(payload, null, 2));

    console.log(`\n${colors.yellow}📨 REQUEST HEADERS:${colors.reset}`);
    Object.entries(headers).forEach(([key, value]) => {
      const displayValue = key === 'Authorization' ? value.substring(0, 20) + '...' : value;
      console.log(`  ${key}: ${displayValue}`);
    });

    console.log(`\n${colors.yellow}🌐 ENDPOINT DETAILS:${colors.reset}`);
    console.log(`  Method: POST`);
    console.log(`  URL: ${endpoint}`);
    console.log(`  Auth Required: Yes`);
    console.log(`  Expected Status: 201 Created`);

    return { payload, headers, endpoint };
  }

  // Run comprehensive test suite
  async runComprehensiveTests() {
    console.log(`\n${colors.bright}${colors.green}🚀 STARTING QUICKCHAT QR AUTHENTICATION TEST SUITE${colors.reset}`);
    console.log(`${colors.cyan}Base URL: ${this.baseUrl}${colors.reset}`);
    console.log(`${colors.cyan}Timestamp: ${new Date().toISOString()}${colors.reset}`);
    console.log(`${colors.cyan}Log Level: ${this.logLevel}${colors.reset}`);

    // 1. Display comprehensive device information
    this.logDetailedDeviceInfo();

    // 2. Test QR Login Generation
    console.log(`\n${colors.bright}${colors.magenta}=== TEST 1: QR LOGIN GENERATION ===${colors.reset}`);
    this.generateQRRequestDetails('login');

    // 3. Test QR Device Link Generation
    console.log(`\n${colors.bright}${colors.magenta}=== TEST 2: QR DEVICE LINK GENERATION ===${colors.reset}`);
    this.generateQRRequestDetails('device_link');

    // 4. Test QR Status Check
    console.log(`\n${colors.bright}${colors.magenta}=== TEST 3: QR STATUS CHECK ===${colors.reset}`);
    const mockSessionId = 'test-session-' + crypto.randomBytes(16).toString('hex');
    this.generateQRStatusRequestDetails(mockSessionId);

    // 5. Test QR Scan
    console.log(`\n${colors.bright}${colors.magenta}=== TEST 4: QR SCAN ===${colors.reset}`);
    const mockQRData = JSON.stringify({
      v: '1.0',
      t: 'login',
      s: mockSessionId,
      c: crypto.randomBytes(16).toString('base64'),
      n: crypto.randomBytes(12).toString('base64'),
      e: Math.floor((Date.now() + 120000) / 1000)
    });
    this.generateQRScanRequestDetails(mockQRData, this.mockAuthToken);

    // 6. Test Device Linking
    console.log(`\n${colors.bright}${colors.magenta}=== TEST 5: DEVICE LINKING ===${colors.reset}`);
    this.generateDeviceLinkRequestDetails(this.mockAuthToken);

    // 7. Security Analysis
    console.log(`\n${colors.bright}${colors.magenta}=== TEST 6: SECURITY ANALYSIS ===${colors.reset}`);
    this.performSecurityAnalysis();

    // 8. Test Summary
    console.log(`\n${colors.bright}${colors.green}🏁 TEST SUITE COMPLETE${colors.reset}`);
    console.log(`${colors.yellow}📊 SUMMARY:${colors.reset}`);
    console.log(`  • Device fingerprint generated and analyzed`);
    console.log(`  • All QR authentication endpoints documented`);
    console.log(`  • Request payloads and headers detailed`);
    console.log(`  • cURL and JavaScript examples provided`);
    console.log(`  • Security considerations analyzed`);
    console.log(`  • Mock data generated for testing`);

    console.log(`\n${colors.cyan}💡 NEXT STEPS:${colors.reset}`);
    console.log(`  1. Start the QuickChat server: npm start`);
    console.log(`  2. Use the generated cURL commands to test endpoints`);
    console.log(`  3. Monitor server logs for detailed request processing`);
    console.log(`  4. Verify fingerprint validation and rate limiting`);
    console.log(`  5. Test with real mobile devices for full integration`);
  }

  // Perform security analysis
  performSecurityAnalysis() {
    console.log(`\n${colors.yellow}🔒 SECURITY ANALYSIS:${colors.reset}`);

    console.log(`\n${colors.cyan}Fingerprint Security:${colors.reset}`);
    console.log(`  • Algorithm: Multi-layer SHA-256/SHA-512`);
    console.log(`  • Entropy Sources: ${this.deviceInfo.network.macAddresses.length + 8} unique identifiers`);
    console.log(`  • Collision Resistance: 2^256 (SHA-256)`);
    console.log(`  • Fingerprint Length: ${this.fingerprint.length} characters`);
    console.log(`  • Uniqueness Score: ${this.calculateUniquenessScore()}/100`);

    console.log(`\n${colors.cyan}Request Security:${colors.reset}`);
    console.log(`  • HTTPS Required: Yes (production)`);
    console.log(`  • Authentication: Bearer JWT tokens`);
    console.log(`  • Rate Limiting: 5 requests/minute for QR generation`);
    console.log(`  • Session Expiry: 90-120 seconds for QR codes`);
    console.log(`  • Device Validation: Fingerprint + IP + User-Agent`);

    console.log(`\n${colors.cyan}Cryptographic Security:${colors.reset}`);
    console.log(`  • Key Exchange: X3DH (Signal Protocol)`);
    console.log(`  • Curve: Curve25519`);
    console.log(`  • Session Keys: Ephemeral + Forward Secrecy`);
    console.log(`  • Message Encryption: AES-256-GCM`);
    console.log(`  • Random Generation: Cryptographically Secure (CSPRNG)`);

    console.log(`\n${colors.cyan}Potential Vulnerabilities:${colors.reset}`);
    console.log(`  • Fingerprint Spoofing: Medium risk (requires system access)`);
    console.log(`  • Session Hijacking: Low risk (short expiry + HTTPS)`);
    console.log(`  • Replay Attacks: Low risk (nonce + timestamp validation)`);
    console.log(`  • Rate Limit Bypass: Low risk (IP + fingerprint based)`);
    console.log(`  • Token Theft: Medium risk (secure storage required)`);
  }

  // Calculate uniqueness score for fingerprint
  calculateUniquenessScore() {
    let score = 0;

    // MAC addresses (high uniqueness)
    score += Math.min(this.deviceInfo.network.macAddresses.length * 15, 30);

    // System identifiers (medium uniqueness)
    score += this.deviceInfo.system.hostname ? 10 : 0;
    score += this.deviceInfo.cpu.model ? 10 : 0;
    score += this.deviceInfo.memory.total ? 5 : 0;

    // Software identifiers (low-medium uniqueness)
    score += this.deviceInfo.process.nodeVersion ? 5 : 0;
    score += this.deviceInfo.browser.timezone ? 10 : 0;
    score += this.deviceInfo.browser.language ? 5 : 0;

    // Hardware identifiers (medium uniqueness)
    score += this.deviceInfo.cpu.count ? 5 : 0;
    score += this.deviceInfo.browser.screenResolution ? 10 : 0;
    score += this.deviceInfo.browser.colorDepth ? 5 : 0;

    // Network topology (high uniqueness)
    score += Math.min(this.deviceInfo.network.ipAddresses.length * 5, 10);

    return Math.min(score, 100);
  }
}

// CLI usage and main execution
if (require.main === module) {
  const tester = new QuickChatQRTester();

  const command = process.argv[2] || 'full';
  const subCommand = process.argv[3];

  console.log(`${colors.bright}${colors.blue}QuickChat QR Authentication Tester${colors.reset}`);
  console.log(`${colors.cyan}Command: ${command}${subCommand ? ' ' + subCommand : ''}${colors.reset}\n`);

  switch (command.toLowerCase()) {
    case 'fingerprint':
    case 'fp':
      console.log(`${colors.yellow}🔐 Generating device fingerprint only...${colors.reset}`);
      tester.logDetailedDeviceInfo();
      break;

    case 'device-info':
    case 'info':
      console.log(`${colors.yellow}📱 Showing detailed device information...${colors.reset}`);
      tester.logDetailedDeviceInfo();
      break;

    case 'qr-login':
    case 'qr':
      console.log(`${colors.yellow}🔲 Testing QR login generation...${colors.reset}`);
      tester.generateQRRequestDetails('login');
      break;

    case 'qr-device-link':
    case 'device-link':
      console.log(`${colors.yellow}🔗 Testing QR device linking...${colors.reset}`);
      tester.generateQRRequestDetails('device_link');
      break;

    case 'mock-requests':
    case 'mock':
      console.log(`${colors.yellow}📋 Generating mock HTTP requests...${colors.reset}`);
      tester.generateQRRequestDetails('login');
      tester.generateQRRequestDetails('device_link');
      const mockSessionId = 'test-session-' + crypto.randomBytes(16).toString('hex');
      tester.generateQRStatusRequestDetails(mockSessionId);
      break;

    case 'security-test':
    case 'security':
      console.log(`${colors.yellow}🔒 Performing security analysis...${colors.reset}`);
      tester.performSecurityAnalysis();
      break;

    case 'auth-token':
    case 'token':
      console.log(`${colors.yellow}🎫 Generating mock authentication token...${colors.reset}`);
      const token = tester.generateMockAuthToken();
      console.log(`\n${colors.green}Mock Auth Token:${colors.reset}`);
      console.log(`${colors.cyan}${token}${colors.reset}`);
      console.log(`\n${colors.yellow}Token Parts:${colors.reset}`);
      const parts = token.split('.');
      console.log(`  Header: ${parts[0]}`);
      console.log(`  Payload: ${parts[1]}`);
      console.log(`  Signature: ${parts[2]}`);
      break;

    case 'help':
    case '-h':
    case '--help':
      console.log(`${colors.yellow}📖 Available Commands:${colors.reset}`);
      console.log(`  ${colors.cyan}fingerprint${colors.reset}     Generate and display device fingerprint`);
      console.log(`  ${colors.cyan}device-info${colors.reset}     Show comprehensive device information`);
      console.log(`  ${colors.cyan}qr-login${colors.reset}        Generate QR login request details`);
      console.log(`  ${colors.cyan}qr-device-link${colors.reset}  Generate QR device linking request details`);
      console.log(`  ${colors.cyan}mock-requests${colors.reset}   Generate all mock HTTP requests`);
      console.log(`  ${colors.cyan}security-test${colors.reset}   Perform security analysis`);
      console.log(`  ${colors.cyan}auth-token${colors.reset}      Generate mock authentication token`);
      console.log(`  ${colors.cyan}full${colors.reset}            Run comprehensive test suite (default)`);
      console.log(`  ${colors.cyan}help${colors.reset}            Show this help message`);
      console.log(`\n${colors.yellow}Examples:${colors.reset}`);
      console.log(`  ${colors.cyan}node device-fingerprint.js${colors.reset}`);
      console.log(`  ${colors.cyan}node device-fingerprint.js fingerprint${colors.reset}`);
      console.log(`  ${colors.cyan}node device-fingerprint.js qr-login${colors.reset}`);
      console.log(`  ${colors.cyan}node device-fingerprint.js security-test${colors.reset}`);
      break;

    case 'full':
    case 'test':
    case 'all':
    default:
      console.log(`${colors.yellow}🚀 Running comprehensive test suite...${colors.reset}`);
      tester.runComprehensiveTests();
      break;
  }

  console.log(`\n${colors.bright}${colors.green}✨ Execution completed successfully!${colors.reset}`);
  console.log(`${colors.cyan}For more options, run: node device-fingerprint.js help${colors.reset}`);
}

// Export for use as module
module.exports = QuickChatQRTester;
