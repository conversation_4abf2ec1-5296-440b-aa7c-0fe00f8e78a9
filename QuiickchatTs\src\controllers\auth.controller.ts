import { FastifyRequest, FastifyReply } from 'fastify';
import * as userService from '../services/user.service';
import * as twilioService from '../services/twilio.service';
import * as zengoService from '../services/zengo.service';
import { Auth } from '../middleware/auth.middleware';
import { ValidationError, NotFoundError, AuthenticationError, ConflictError, DatabaseError, ExternalServiceError } from '../utils/errors';
import { formatPhoneNumber, isValidPhoneNumber, isDevelopment } from '../utils/helpers';
import { logger } from '../utils/logger';
import {
  ApiResponse,
  RegisterRequest,
  VerifyRequest,
  LoginRequest,
  VerifyLoginRequest,
  RefreshTokenRequest,
  UserQuery,
  AuthenticatedRequest
} from '../types';

export const register = async (request: FastifyRequest<{ Body: RegisterRequest }>, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    throw new ValidationError('Request body is required');
  }

  const { phone } = request.body;

  if (!phone || phone.trim().length === 0) {
    throw new ValidationError('Phone number is required');
  }

  let formattedPhone: string;
  try {
    formattedPhone = formatPhoneNumber(phone);
  } catch (error) {
    logger.warn(`Phone formatting failed: ${phone}`);
    throw new ValidationError('Invalid phone number format. Please include country code');
  }

  if (!isValidPhoneNumber(formattedPhone)) {
    throw new ValidationError('Invalid phone number format. Please provide a valid phone number with country code');
  }

  logger.info(`Registration attempt for phone: '${formattedPhone}'`);

  try {
    await userService.getUserByPhone(formattedPhone);
    logger.warn(`Registration attempt for existing user: ${formattedPhone}`);
    throw new ConflictError('User with this phone number already exists. Try logging in instead');
  } catch (error) {
    if (error instanceof ConflictError) {
      throw error;
    }
    if (!(error instanceof NotFoundError)) {
      logger.error('Database error checking phone:', error);
      throw new DatabaseError('Unable to verify phone number availability');
    }
    logger.info(`Phone ${formattedPhone} available for registration`);
  }

  try {
    const code = await userService.createVerification(formattedPhone);

    let smsSent = false;
    let smsError: string | null = null;

    if (twilioService.isServiceEnabled()) {
      try {
        await twilioService.sendOtp(formattedPhone, code);
        logger.info(`SMS sent to ${formattedPhone}`);
        smsSent = true;
      } catch (error: any) {
        logger.warn(`SMS failed for ${formattedPhone}: ${error.message}`);
        smsError = error.message;

        if (error instanceof ExternalServiceError) {
          if (error.message.includes('Invalid phone number') ||
              error.message.includes('not a valid mobile number')) {
            throw new ValidationError('Invalid phone number for SMS delivery');
          }
        }
      }
    } else {
      logger.info('SMS service disabled - using development mode');
    }

    const isDevMode = isDevelopment() || !smsSent;
    const responseData: any = {
      phone: formattedPhone,
      sms_sent: smsSent
    };

    if (isDevMode) {
      responseData.verification_code = code;
      responseData.note = "Development mode - code included";
    }

    if (!smsSent && smsError) {
      responseData.sms_error = "SMS delivery failed - using development mode";
    }

    return reply.status(201).send({
      success: true,
      message: smsSent ? 'Verification code sent via SMS' : 'Verification code generated (check logs in development)',
      data: responseData
    });
  } catch (error: any) {
    if (error instanceof ValidationError || error instanceof ExternalServiceError) {
      throw error;
    }
    logger.error('Registration error:', error);
    if (error.name === 'MongoError' || error.message.includes('database')) {
      throw new DatabaseError('Registration service temporarily unavailable');
    }
    throw new DatabaseError('Registration failed');
  }
};

export const verify = async (request: FastifyRequest<{ Body: VerifyRequest }>, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    throw new ValidationError('Request body is required');
  }

  const { phone, code } = request.body;

  if (!phone || phone.trim().length === 0) {
    throw new ValidationError('Phone number is required');
  }

  if (!code || code.trim().length === 0) {
    throw new ValidationError('Verification code is required');
  }

  const formattedPhone = formatPhoneNumber(phone);
  logger.info(`Verifying code for phone: '${formattedPhone}'`);

  try {
    const verified = await userService.verifyCode(formattedPhone, code);

    if (!verified) {
      throw new ValidationError('Invalid or expired verification code');
    }

    const user = await userService.createUser(formattedPhone);
    const authTokens = Auth.generateTokens(user.phone);

    if (zengoService.isZengoEnabled()) {
      try {
        await zengoService.registerUser(
          user._id?.toString() || user.phone,
          user.username || undefined,
          user.profile_picture || undefined
        );
        logger.info(`Zengo registration successful: ${user.phone}`);
      } catch (zengoError) {
        logger.warn(`Zengo registration failed: ${zengoError}`);
      }
    }

    return reply.status(200).send({
      success: true,
      message: 'Registration successful',
      data: {
        user: {
          id: user._id?.toString(),
          email: user.email,
          username: user.username,
          phone: user.phone,
          profile_picture: user.profile_picture,
          is_verified: user.is_verified,
          status: user.status,
          bio: user.bio,
          address: user.address,
          created_at: user.createdAt
        },
        auth_tokens: {
          ...authTokens,
          token_type: "Bearer"
        }
      }
    });
  } catch (error: any) {
    if (error instanceof ValidationError) {
      throw error;
    }
    logger.error('Verification error:', error);
    if (error.name === 'MongoError' || error.message.includes('database')) {
      throw new DatabaseError('Verification service temporarily unavailable');
    }
    throw new DatabaseError('Verification failed');
  }
};

export const login = async (request: FastifyRequest<{ Body: LoginRequest }>, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    throw new ValidationError('Request body is required');
  }

  const { phone } = request.body;

  if (!phone || phone.trim().length === 0) {
    throw new ValidationError('Phone number is required');
  }

  const formattedPhone = formatPhoneNumber(phone);
  logger.info(`Login request for phone: '${formattedPhone}'`);

  try {
    const user = await userService.getUserByPhone(formattedPhone);

    if (!user.is_verified) {
      throw new AuthenticationError('Account not verified. Please complete registration first');
    }

    const code = await userService.createVerification(formattedPhone);

    let smsSent = false;
    let smsError: string | null = null;

    if (twilioService.isServiceEnabled()) {
      try {
        await twilioService.sendOtp(formattedPhone, code);
        logger.info(`SMS sent to ${formattedPhone}`);
        smsSent = true;
      } catch (error: any) {
        logger.warn(`SMS failed for ${formattedPhone}: ${error.message}`);
        smsError = error.message;
      }
    } else {
      logger.info('SMS service disabled - using development mode');
    }

    const isDevMode = isDevelopment() || !smsSent;
    const responseData: any = {
      phone: formattedPhone,
      sms_sent: smsSent
    };

    if (isDevMode) {
      responseData.verification_code = code;
      responseData.note = "Development mode - code included";
    }

    if (!smsSent && smsError) {
      responseData.sms_error = "SMS delivery failed - using development mode";
    }

    return reply.status(200).send({
      success: true,
      message: smsSent ? 'Verification code sent' : 'Verification code generated',
      data: responseData
    });
  } catch (error: any) {
    if (error instanceof NotFoundError) {
      throw new NotFoundError('No account found with this phone number. Please register first');
    }
    if (error instanceof AuthenticationError) {
      throw error;
    }
    logger.error('Login error:', error);
    if (error.name === 'MongoError' || error.message.includes('database')) {
      throw new DatabaseError('Login service temporarily unavailable');
    }
    throw new DatabaseError('Login failed');
  }
};

export const verifyLogin = async (request: FastifyRequest<{ Body: VerifyLoginRequest }>, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    throw new ValidationError('Request body is required');
  }

  const { phone, code } = request.body;

  if (!phone || phone.trim().length === 0) {
    throw new ValidationError('Phone number is required');
  }

  if (!code || code.trim().length === 0) {
    throw new ValidationError('Verification code is required');
  }

  const formattedPhone = formatPhoneNumber(phone);
  logger.info(`Verifying login for phone: '${formattedPhone}'`);

  try {
    const verified = await userService.verifyCode(formattedPhone, code);

    if (!verified) {
      throw new ValidationError('Invalid or expired verification code');
    }

    const user = await userService.getUserByPhone(formattedPhone);
    const authTokens = Auth.generateTokens(user.phone);

    // Note: Zengo registration is only done during initial user registration (verify endpoint),
    // not during subsequent logins to avoid unnecessary API calls

    return reply.status(200).send({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user._id?.toString(),
          email: user.email,
          username: user.username,
          phone: user.phone,
          profile_picture: user.profile_picture,
          is_verified: user.is_verified,
          status: user.status,
          bio: user.bio,
          address: user.address,
          created_at: user.createdAt
        },
        auth_tokens: {
          ...authTokens,
          token_type: "Bearer"
        }
      }
    });
  } catch (error: any) {
    if (error instanceof ValidationError) {
      throw error;
    }
    logger.error('Login verification error:', error);
    if (error.name === 'MongoError' || error.message.includes('database')) {
      throw new DatabaseError('Login verification service temporarily unavailable');
    }
    throw new DatabaseError('Login verification failed');
  }
};

export const refreshToken = async (request: FastifyRequest<{ Body: RefreshTokenRequest }>, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { refresh_token } = request.body;

  if (!refresh_token) {
    return reply.status(400).send({
      success: false,
      message: 'Refresh token is required'
    });
  }

  logger.info('Refresh token request received');

  try {
    const claims = Auth.validateRefreshToken(refresh_token);
    logger.info(`Refresh token validated successfully for phone: ${claims.phone}`);

    const phone = claims.phone;
    const user = await userService.getUserByPhone(phone);
    const authTokens = Auth.generateTokens(user.phone);

    return reply.status(200).send({
      success: true,
      message: 'Tokens refreshed successfully',
      data: {
        auth_tokens: {
          ...authTokens,
          token_type: "Bearer"
        }
      }
    });
  } catch (error: any) {
    logger.error(`Failed to refresh token: ${error}`);
    return reply.status(401).send({
      success: false,
      message: 'Invalid refresh token'
    });
  }
};

export const resendVerificationCode = async (request: FastifyRequest<{ Body: RegisterRequest }>, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { phone } = request.body;

  if (!phone || phone.trim().length === 0) {
    return reply.status(400).send({
      success: false,
      message: 'Phone number is required'
    });
  }

  const formattedPhone = formatPhoneNumber(phone);
  logger.info(`Resending verification code for phone: '${formattedPhone}'`);

  try {
    const code = await userService.createVerification(formattedPhone);

    let smsSent = false;
    try {
      await twilioService.sendOtp(formattedPhone, code);
      logger.info(`SMS sent successfully to ${formattedPhone}`);
      smsSent = true;
    } catch (error) {
      logger.warn(`Failed to send SMS to ${formattedPhone}: ${error}. Code will be included in response for development.`);
    }

    const isDevMode = isDevelopment() || !smsSent;

    const responseData: any = {
      phone: formattedPhone,
      sms_sent: smsSent
    };

    if (isDevMode) {
      responseData.verification_code = code;
      responseData.note = "Will remove the code in response soon";
    }

    return reply.status(200).send({
      success: true,
      message: smsSent ? 'Verification code resent' : 'Verification code generated',
      data: responseData
    });
  } catch (error: any) {
    logger.error('Error resending verification code:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to resend code'
    });
  }
};

export const initUser = async (request: FastifyRequest<{ Querystring: UserQuery }>, reply: FastifyReply): Promise<any> => {
  const { phone, user_id } = request.query;

  if (phone) {
    const formattedPhone = formatPhoneNumber(phone);

    try {
      const user = await userService.getUserByPhone(formattedPhone);

      if (user._id) {
        await userService.updateLastSeen(user._id.toString());
      }

      return reply.status(200).send({
        success: true,
        message: 'User retrieved successfully',
        data: {
          id: user._id?.toString(),
          email: user.email,
          username: user.username,
          phone: user.phone,
          profile_picture: user.profile_picture,
          is_verified: user.is_verified,
          status: user.status,
          bio: user.bio,
          address: user.address,
          created_at: user.createdAt
        }
      });
    } catch (error: any) {
      if (error instanceof NotFoundError) {
        return reply.status(404).send({
          success: false,
          message: 'User not found'
        });
      }

      logger.error('Error getting user by phone:', error);
      return reply.status(500).send({
        success: false,
        message: 'Failed to retrieve user'
      });
    }
  }

  if (user_id) {
    try {
      const user = await userService.getUserById(user_id);
      await userService.updateLastSeen(user_id);

      return reply.status(200).send({
        success: true,
        message: 'User initialized successfully',
        data: {
          user: {
            id: user._id?.toString(),
            email: user.email,
            username: user.username,
            phone: user.phone,
            profile_picture: user.profile_picture,
            is_verified: user.is_verified,
            status: user.status,
            bio: user.bio,
            address: user.address,
            created_at: user.createdAt
          }
        }
      });
    } catch (error: any) {
      if (error instanceof NotFoundError) {
        return reply.status(404).send({
          success: false,
          message: 'User not found'
        });
      }

      logger.error('Error getting user by ID:', error);
      return reply.status(500).send({
        success: false,
        message: 'Failed to initialize user'
      });
    }
  }

  return reply.status(400).send({
    success: false,
    message: 'Either phone or user_id is required'
  });
};
