import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import {
  uploadContacts,
  getContacts,
  syncContacts
} from '../controllers/contact.controller';
import { BulkContactsRequest } from '../types';
import { authenticateRequest } from '../middleware/auth.middleware';

export async function contactRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.post('/upload', { preHandler: authenticateRequest }, uploadContacts as any);
  fastify.get('/', { preHandler: authenticateRequest }, getContacts as any);
  fastify.post('/sync', { preHandler: authenticateRequest }, syncContacts as any);
}
