import { FastifyInstance } from 'fastify';
import { authenticateRequest } from '../middleware/auth.middleware';
import { uploadChatMedia, uploadMultipleChatMedia, deleteUploadedMedia } from '../controllers/upload.controller';

export const uploadRoutes = async (fastify: FastifyInstance) => {
  fastify.addHook('preHandler', authenticateRequest);

  fastify.post('/chat-media', {
    schema: {
      description: 'Upload single media file for chat (images, videos, audio, documents)',
      tags: ['Upload'],
      security: [{ bearerAuth: [] }],
      consumes: ['multipart/form-data'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                url: { type: 'string' },
                public_id: { type: 'string' },
                media_type: { type: 'string' },
                file_size: { type: 'number' },
                mime_type: { type: 'string' },
                filename: { type: 'string' },
                uploaded_at: { type: 'string' }
              }
            }
          }
        },
        400: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, uploadChatMedia);

  fastify.post('/chat-media/multiple', {
    schema: {
      description: 'Upload multiple media files for chat (max 10 files)',
      tags: ['Upload'],
      security: [{ bearerAuth: [] }],
      consumes: ['multipart/form-data'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                files: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      url: { type: 'string' },
                      public_id: { type: 'string' },
                      media_type: { type: 'string' },
                      file_size: { type: 'number' },
                      mime_type: { type: 'string' },
                      filename: { type: 'string' },
                      uploaded_at: { type: 'string' }
                    }
                  }
                },
                total_uploaded: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, uploadMultipleChatMedia);

  fastify.delete('/chat-media/:public_id', {
    schema: {
      description: 'Delete uploaded media file',
      tags: ['Upload'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          public_id: { type: 'string' }
        },
        required: ['public_id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, deleteUploadedMedia);
};
