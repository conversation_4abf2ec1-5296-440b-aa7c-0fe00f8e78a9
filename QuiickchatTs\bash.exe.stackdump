Stack trace:
Frame         Function      Args
0007FFFFBE20  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBE20, 0007FFFFAD20) msys-2.0.dll+0x1FE8E
0007FFFFBE20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC0F8) msys-2.0.dll+0x67F9
0007FFFFBE20  000210046832 (000210286019, 0007FFFFBCD8, 0007FFFFBE20, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE20  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBE20  000210068E24 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC100  00021006A225 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCA19A0000 ntdll.dll
7FFCA11B0000 KERNEL32.DLL
7FFC9EAD0000 KERNELBASE.dll
7FFCA1760000 USER32.dll
7FFC9EF90000 win32u.dll
7FFCA1930000 GDI32.dll
7FFC9F110000 gdi32full.dll
7FFC9F5F0000 msvcp_win.dll
7FFC9EFC0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCA06A0000 advapi32.dll
7FFCA0130000 msvcrt.dll
7FFCA05F0000 sechost.dll
7FFCA1290000 RPCRT4.dll
7FFC9DA50000 CRYPTBASE.DLL
7FFC9F250000 bcryptPrimitives.dll
7FFCA1520000 IMM32.DLL
