import mongoose, { Document, Schema } from 'mongoose';

export interface IStatus extends Document {
  _id: mongoose.Types.ObjectId;
  user_id: string;
  content_type: 'text' | 'image' | 'video';
  content?: string; // For text status content
  content_url?: string; // For image/video URLs
  caption?: string;
  background_color?: string;
  text_color?: string; // Add text color support
  font_style?: string;
  privacy_setting: 'Everyone' | 'Contacts' | 'ContactsExcept' | 'OnlyShare';
  allowed_contacts: string[];
  blocked_contacts: string[];
  view_count: number;
  viewers: string[];
  is_active: boolean;
  expires_at: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IStatusView extends Document {
  _id: mongoose.Types.ObjectId;
  status_id: string;
  viewer_id: string;
  viewed_at: Date;
}

const StatusSchema = new Schema<IStatus>({
  user_id: { type: String, required: true },
  content_type: {
    type: String,
    enum: ['text', 'image', 'video'],
    required: true
  },
  content: { type: String }, // For text status content
  content_url: { type: String }, // For image/video URLs
  caption: { type: String },
  background_color: { type: String },
  text_color: { type: String }, // Add text color support
  font_style: { type: String },
  privacy_setting: {
    type: String,
    enum: ['Everyone', 'Contacts', 'ContactsExcept', 'OnlyShare'],
    default: 'Contacts'
  },
  allowed_contacts: [{ type: String }],
  blocked_contacts: [{ type: String }],
  view_count: { type: Number, default: 0 },
  viewers: [{ type: String }],
  is_active: { type: Boolean, default: true },
  expires_at: {
    type: Date,
    default: () => new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
  }
}, {
  timestamps: true
});

const StatusViewSchema = new Schema<IStatusView>({
  status_id: { type: String, required: true },
  viewer_id: { type: String, required: true },
  viewed_at: { type: Date, default: Date.now }
}, {
  timestamps: true
});

StatusSchema.index({ user_id: 1, createdAt: -1 });
StatusSchema.index({ expires_at: 1 }, { expireAfterSeconds: 0 });
StatusViewSchema.index({ status_id: 1, viewer_id: 1 }, { unique: true });

export const Status = mongoose.model<IStatus>('Status', StatusSchema);
export const StatusView = mongoose.model<IStatusView>('StatusView', StatusViewSchema);
