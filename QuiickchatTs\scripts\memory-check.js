#!/usr/bin/env node

/**
 * Memory monitoring script for build processes
 * Usage: node scripts/memory-check.js
 */

const formatBytes = (bytes) => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

const checkMemory = () => {
  const usage = process.memoryUsage();
  
  console.log('📊 Memory Usage Report:');
  console.log('─'.repeat(40));
  console.log(`RSS (Resident Set Size): ${formatBytes(usage.rss)}`);
  console.log(`Heap Total: ${formatBytes(usage.heapTotal)}`);
  console.log(`Heap Used: ${formatBytes(usage.heapUsed)}`);
  console.log(`External: ${formatBytes(usage.external)}`);
  console.log(`Array Buffers: ${formatBytes(usage.arrayBuffers)}`);
  console.log('─'.repeat(40));
  
  // Check if we're approaching memory limits
  const heapUsedMB = usage.heapUsed / 1024 / 1024;
  const heapTotalMB = usage.heapTotal / 1024 / 1024;
  
  if (heapUsedMB > 1500) {
    console.log('⚠️  WARNING: High memory usage detected!');
    console.log('   Consider increasing --max-old-space-size');
  }
  
  if (heapTotalMB > 3500) {
    console.log('🚨 CRITICAL: Approaching memory limit!');
    console.log('   Build may fail due to memory constraints');
  }
  
  console.log(`Memory efficiency: ${Math.round((heapUsedMB / heapTotalMB) * 100)}%`);
};

// Run memory check
checkMemory();

// If running as a monitoring script, check every 5 seconds
if (process.argv.includes('--monitor')) {
  console.log('🔍 Starting memory monitoring (Ctrl+C to stop)...\n');
  setInterval(checkMemory, 5000);
}
