import mongoose, { Schema, Document } from 'mongoose';

export interface IVerification extends Document {
  _id: mongoose.Types.ObjectId;
  phone: string;
  code: string;
  expires_at: Date;
  attempts: number;
  verified: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

const VerificationSchema = new Schema<IVerification>({
  phone: {
    type: String,
    required: true
  },
  code: {
    type: String,
    required: true
  },
  expires_at: {
    type: Date,
    required: true
  },
  attempts: {
    type: Number,
    default: 0
  },
  verified: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

VerificationSchema.index({ phone: 1 });
VerificationSchema.index({ expires_at: 1 }, { expireAfterSeconds: 0 });

export const Verification = mongoose.model<IVerification>('Verification', VerificationSchema);

export interface ITwoFactorAuth extends Document {
  _id: mongoose.Types.ObjectId;
  user_id: string;
  secret: string;
  backup_codes: string[];
  enabled: boolean;
  created_at: Date;
  updated_at: Date;
}

const TwoFactorAuthSchema = new Schema<ITwoFactorAuth>({
  user_id: {
    type: String,
    required: true,
    unique: true
  },
  secret: { 
    type: String, 
    required: true 
  },
  backup_codes: [{ 
    type: String 
  }],
  enabled: { 
    type: Boolean, 
    default: false 
  },
  created_at: { 
    type: Date, 
    default: Date.now 
  },
  updated_at: { 
    type: Date, 
    default: Date.now 
  }
});

TwoFactorAuthSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});


export const TwoFactorAuth = mongoose.model<ITwoFactorAuth>('TwoFactorAuth', TwoFactorAuthSchema);
