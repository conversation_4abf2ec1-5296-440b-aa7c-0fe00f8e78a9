import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import {
  register,
  verify,
  login,
  verifyLogin,
  refreshToken,
  resendVerificationCode,
  initUser
} from '../controllers/auth.controller';
import {
  RegisterRequest,
  VerifyRequest,
  LoginRequest,
  VerifyLoginRequest,
  RefreshTokenRequest,
  UserQuery
} from '../types';

export async function authRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  fastify.post('/register', register as any);
  fastify.post('/verify', verify as any);
  fastify.post('/login', login as any);
  fastify.post('/verify-login', verifyLogin as any);
  fastify.post('/refresh-token', refreshToken as any);
  fastify.post('/resend-code', resendVerificationCode as any);
  fastify.get('/init', initUser as any);
}
