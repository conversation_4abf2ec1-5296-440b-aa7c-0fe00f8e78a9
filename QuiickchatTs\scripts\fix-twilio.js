#!/usr/bin/env node

/**
 * Twilio Credentials Fix Script
 * This script helps you update and test your Twilio credentials
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function fixTwilioCredentials() {
  console.log('🔧 Twilio Credentials Fix Script\n');
  
  console.log('Please get your credentials from: https://console.twilio.com/');
  console.log('You need: Account SID, Auth Token, and a verified phone number\n');

  try {
    // Get new credentials from user
    const accountSid = await question('Enter your Twilio Account SID: ');
    const authToken = await question('Enter your Twilio Auth Token: ');
    const fromNumber = await question('Enter your Twilio phone number (e.g., +**********): ');

    if (!accountSid || !authToken || !fromNumber) {
      console.log('❌ All fields are required!');
      process.exit(1);
    }

    // Validate phone number format
    if (!fromNumber.match(/^\+[1-9]\d{1,14}$/)) {
      console.log('❌ Invalid phone number format. Use international format: +**********');
      process.exit(1);
    }

    // Read current .env file
    const envPath = path.join(__dirname, '..', '.env');
    let envContent = '';
    
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
    }

    // Update Twilio credentials
    const updatedEnv = envContent
      .replace(/TWILIO_ACCOUNT_SID=.*/, `TWILIO_ACCOUNT_SID=${accountSid}`)
      .replace(/TWILIO_AUTH_TOKEN=.*/, `TWILIO_AUTH_TOKEN=${authToken}`)
      .replace(/TWILIO_FROM_NUMBER=.*/, `TWILIO_FROM_NUMBER=${fromNumber}`)
      .replace(/TWILIO_ENABLED=.*/, 'TWILIO_ENABLED=true');

    // If credentials don't exist, add them
    if (!envContent.includes('TWILIO_ACCOUNT_SID')) {
      updatedEnv += `\nTWILIO_ACCOUNT_SID=${accountSid}`;
    }
    if (!envContent.includes('TWILIO_AUTH_TOKEN')) {
      updatedEnv += `\nTWILIO_AUTH_TOKEN=${authToken}`;
    }
    if (!envContent.includes('TWILIO_FROM_NUMBER')) {
      updatedEnv += `\nTWILIO_FROM_NUMBER=${fromNumber}`;
    }
    if (!envContent.includes('TWILIO_ENABLED')) {
      updatedEnv += `\nTWILIO_ENABLED=true`;
    }

    // Write updated .env file
    fs.writeFileSync(envPath, updatedEnv);
    console.log('\n✅ .env file updated successfully!');

    // Test the credentials
    console.log('\n🧪 Testing new credentials...');
    
    const twilio = require('twilio');
    const client = twilio(accountSid, authToken);

    try {
      const account = await client.api.accounts(accountSid).fetch();
      console.log(`✅ Credentials valid! Account: ${account.friendlyName}`);
      
      // Check phone number
      const phoneNumbers = await client.incomingPhoneNumbers.list();
      const ownedNumber = phoneNumbers.find(num => num.phoneNumber === fromNumber);
      
      if (ownedNumber) {
        console.log(`✅ Phone number ${fromNumber} is verified and owned by your account`);
        console.log(`✅ SMS capability: ${ownedNumber.capabilities.sms ? 'Enabled' : 'Disabled'}`);
      } else {
        console.log(`❌ Phone number ${fromNumber} is not owned by your account`);
        console.log('Available numbers:');
        phoneNumbers.forEach(num => {
          console.log(`  - ${num.phoneNumber} (SMS: ${num.capabilities.sms})`);
        });
      }

      // Check balance
      const balance = await client.balance.fetch();
      console.log(`✅ Account balance: ${balance.balance} ${balance.currency}`);

      if (parseFloat(balance.balance) <= 0) {
        console.log('⚠️  Warning: Account balance is low! Add credits to send SMS.');
      }

    } catch (error) {
      console.log(`❌ Credential test failed: ${error.message}`);
      console.log(`Error code: ${error.code || 'Unknown'}`);
    }

    console.log('\n🎉 Setup complete! You can now test SMS sending.');
    console.log('Run: npm run dev');
    console.log('Then test: POST http://localhost:8080/api/v1/system/test-sms');

  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  } finally {
    rl.close();
  }
}

fixTwilioCredentials();
