import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import {
  createStatus,
  uploadStatusMedia,
  getMyStatuses,
  getContactStatuses,
  getUserStatuses,
  viewStatus,
  getStatusViews,
  deleteStatus,
  getPrivacySettings,
  updatePrivacySettings
} from '../controllers/status.controller';

import { authenticateRequest } from '../middleware/auth.middleware';

export async function statusRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // Create status (text only)
  fastify.post('/', { preHandler: authenticateRequest }, createStatus as any);

  // Upload status with media (image/video)
  fastify.post('/upload', { preHandler: authenticateRequest }, uploadStatusMedia as any);

  // Get contact statuses (status feed)
  fastify.get('/feed', { preHandler: authenticateRequest }, getContactStatuses as any);

  // Get my statuses
  fastify.get('/my', { preHandler: authenticateRequest }, getMyStatuses as any);

  // Get statuses for a specific user
  fastify.get('/user/:user_id', { preHandler: authenticateRequest }, getUserStatuses as any);

  // View a status (mark as viewed)
  fastify.post('/view', { preHandler: authenticateRequest }, viewStatus as any);

  // Get who viewed a specific status
  fastify.get('/:status_id/views', { preHandler: authenticateRequest }, getStatusViews as any);

  // Delete a status
  fastify.delete('/:status_id', { preHandler: authenticateRequest }, deleteStatus as any);

  // Privacy settings routes
  fastify.get('/privacy', { preHandler: authenticateRequest }, getPrivacySettings as any);
  fastify.put('/privacy', { preHandler: authenticateRequest }, updatePrivacySettings as any);
}
