import { User, IUser } from '../models/user.model';
import { Verification } from '../models/verification.model';
import { generateVerificationCode } from '../utils/helpers';
import { DatabaseError, ValidationError, NotFoundError } from '../utils/errors';
import { logger } from '../utils/logger';

export const createVerification = async (phone: string): Promise<string> => {
  try {
    const code = generateVerificationCode();
    const expiresAt = new Date(Date.now() + (10 * 60 * 1000)); // 10 minutes from now

    const existingVerification = await Verification.findOne({ phone });

    if (existingVerification) {
      existingVerification.code = code;
      existingVerification.expires_at = expiresAt;
      existingVerification.attempts = 0;
      existingVerification.verified = false;
      await existingVerification.save();
    } else {
      const verification = new Verification({
        phone,
        code,
        expires_at: expiresAt,
        attempts: 0,
        verified: false
      });
      await verification.save();
    }

    return code;
  } catch (error) {
    logger.error('Failed to create verification:', error);
    throw new DatabaseError(`Failed to create verification record: ${error}`);
  }
};

export const verifyCode = async (phone: string, code: string): Promise<boolean> => {
  try {
    const verification = await Verification.findOne({ phone });

    if (!verification) {
      throw new ValidationError('No verification record found for this phone number');
    }

    const now = new Date();

    if (verification.expires_at < now) {
      throw new ValidationError('Verification code has expired');
    }

    if (verification.code !== code) {
      verification.attempts += 1;
      await verification.save();

      if (verification.attempts >= 3) {
        throw new ValidationError('Too many failed attempts. Please request a new code.');
      }

      throw new ValidationError('Invalid verification code');
    }

    verification.verified = true;
    await verification.save();

    return true;
  } catch (error) {
    if (error instanceof ValidationError) {
      throw error;
    }
    logger.error('Failed to verify code:', error);
    throw new DatabaseError(`Failed to verify code: ${error}`);
  }
};

export const createUser = async (phone: string, userData?: { username?: string; email?: string }): Promise<IUser> => {
  try {
    const existingUser = await User.findOne({ phone });

    if (existingUser) {
      existingUser.is_verified = true;
      existingUser.last_seen = new Date();

      if (!existingUser.email && userData?.email) {
        existingUser.email = userData.email;
      } else if (!existingUser.email) {
        existingUser.email = `${phone.replace('+', '')}@q.quiickchat.net`;
      }

      await existingUser.save();
      return existingUser;
    } else {
      const email = userData?.email || `${phone.replace('+', '')}@q.quiickchat.net`;

      const user = new User({
        email,
        username: userData?.username,
        phone,
        profile_picture: undefined,
        is_verified: true,
        last_seen: new Date(),
        contacts: [],
        blocked_users: [],
        status: undefined,
        calls: [],
        videos: [],
        chats: [],
        tokens: [],
        bio: undefined,
        address: undefined,
        identity_key: undefined,
        signed_pre_key: undefined,
        pre_key: undefined,
        one_time_keys: [],
        last_key_update: undefined,
        device_id: undefined,
        registration_id: undefined
      });

      await user.save();
      return user;
    }
  } catch (error) {
    logger.error('Failed to create user:', error);
    throw new DatabaseError(`Failed to create user: ${error}`);
  }
};

export const getUserByPhone = async (phone: string): Promise<IUser> => {
  try {
    const user = await User.findOne({ phone });

    if (!user) {
      throw new NotFoundError('User not found');
    }

    return user;
  } catch (error) {
    if (error instanceof NotFoundError) {
      throw error;
    }
    logger.error('Failed to get user by phone:', error);
    throw new DatabaseError(`Failed to get user by phone: ${error}`);
  }
};

export const getUserById = async (userId: string): Promise<IUser> => {
  try {
    const user = await User.findById(userId);

    if (!user) {
      throw new NotFoundError('User not found');
    }

    return user;
  } catch (error) {
    if (error instanceof NotFoundError) {
      throw error;
    }
    logger.error('Failed to get user by ID:', error);
    throw new DatabaseError(`Failed to get user by ID: ${error}`);
  }
};

export const updateLastSeen = async (userId: string): Promise<void> => {
  try {
    await User.findByIdAndUpdate(userId, {
      last_seen: new Date()
    });
  } catch (error) {
    logger.error('Failed to update last seen:', error);
    throw new DatabaseError(`Failed to update last seen: ${error}`);
  }
};

export const updateProfile = async (userId: string, updates: Partial<IUser>): Promise<IUser> => {
  try {
    const user = await User.findByIdAndUpdate(
      userId,
      updates,
      { new: true }
    );

    if (!user) {
      throw new NotFoundError('User not found');
    }

    return user;
  } catch (error) {
    if (error instanceof NotFoundError) {
      throw error;
    }
    logger.error('Failed to update profile:', error);
    throw new DatabaseError(`Failed to update profile: ${error}`);
  }
};

export const deleteUser = async (userId: string): Promise<void> => {
  try {
    const result = await User.findByIdAndDelete(userId);

    if (!result) {
      throw new NotFoundError('User not found');
    }

    await Verification.deleteMany({ phone: result.phone });
  } catch (error) {
    if (error instanceof NotFoundError) {
      throw error;
    }
    logger.error('Failed to delete user:', error);
    throw new DatabaseError(`Failed to delete user: ${error}`);
  }
};
