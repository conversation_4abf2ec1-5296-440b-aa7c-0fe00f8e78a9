import mongoose, { Document, Schema } from 'mongoose';

export interface IQRSession extends Document {
  session_id: string;
  qr_code_data: string;
  session_type: 'login' | 'device_link';
  status: 'pending' | 'scanned' | 'approved' | 'rejected' | 'expired' | 'consumed';
  initiator_device_id?: string;
  initiator_ip: string;
  initiator_user_agent: string;
  initiator_fingerprint: string;
  scanner_phone?: string;
  scanner_device_id?: string;
  scanner_ip?: string;
  scanner_user_agent?: string;
  approval_data?: {
    device_name: string;
    device_type: 'ios' | 'android' | 'web' | 'desktop';
    identity_key: string;
    signed_pre_key: string;
    pre_key_bundle: string;
    registration_id: string;
    device_fingerprint: string;
  };
  security_data: {
    nonce: string;
    challenge: string;
    salt: string;
    key_exchange_data?: string;
  };
  expires_at: Date;
  scanned_at?: Date;
  approved_at?: Date;
  consumed_at?: Date;
  created_at: Date;
  updated_at: Date;
}

const QRSessionSchema = new Schema<IQRSession>({
  session_id: { type: String, required: true, unique: true, index: true },
  qr_code_data: { type: String, required: true },
  session_type: { type: String, enum: ['login', 'device_link'], required: true },
  status: { 
    type: String, 
    enum: ['pending', 'scanned', 'approved', 'rejected', 'expired', 'consumed'], 
    default: 'pending',
    index: true
  },
  initiator_device_id: { type: String },
  initiator_ip: { type: String, required: true },
  initiator_user_agent: { type: String, required: true },
  initiator_fingerprint: { type: String, required: true },
  scanner_phone: { type: String },
  scanner_device_id: { type: String },
  scanner_ip: { type: String },
  scanner_user_agent: { type: String },
  approval_data: {
    device_name: { type: String },
    device_type: { type: String, enum: ['ios', 'android', 'web', 'desktop'] },
    identity_key: { type: String },
    signed_pre_key: { type: String },
    pre_key_bundle: { type: String },
    registration_id: { type: String },
    device_fingerprint: { type: String }
  },
  security_data: {
    nonce: { type: String, required: true },
    challenge: { type: String, required: true },
    salt: { type: String, required: true },
    key_exchange_data: { type: String }
  },
  expires_at: { type: Date, required: true },
  scanned_at: { type: Date },
  approved_at: { type: Date },
  consumed_at: { type: Date },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

QRSessionSchema.index({ expires_at: 1 }, { expireAfterSeconds: 0 });
QRSessionSchema.index({ status: 1, created_at: -1 });
QRSessionSchema.index({ scanner_phone: 1, status: 1 });

export const QRSession = mongoose.model<IQRSession>('QRSession', QRSessionSchema);
