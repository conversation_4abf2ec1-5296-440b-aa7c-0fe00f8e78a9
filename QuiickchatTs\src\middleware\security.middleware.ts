import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { AuditLog } from '../models/device-link.model';
import { logger } from '../utils/logger';
import { ValidationError, AuthenticationError } from '../utils/errors';
import { CryptoService } from '../services/crypto.service';

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
}

interface SecurityCheck {
  isValid: boolean;
  riskScore: number;
  reasons: string[];
}

class RateLimiter {
  private requests = new Map<string, { count: number; resetTime: number }>();
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;
  }

  async checkLimit(identifier: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;

    let requestData = this.requests.get(identifier);
    
    if (!requestData || requestData.resetTime <= now) {
      requestData = {
        count: 0,
        resetTime: now + this.config.windowMs
      };
    }

    requestData.count++;
    this.requests.set(identifier, requestData);

    this.cleanup(windowStart);

    const allowed = requestData.count <= this.config.maxRequests;
    const remaining = Math.max(0, this.config.maxRequests - requestData.count);

    return {
      allowed,
      remaining,
      resetTime: requestData.resetTime
    };
  }

  private cleanup(cutoff: number): void {
    for (const [key, data] of this.requests.entries()) {
      if (data.resetTime <= cutoff) {
        this.requests.delete(key);
      }
    }
  }
}

export class SecurityMiddleware {
  private static qrRateLimiter = new RateLimiter({ windowMs: 60000, maxRequests: 5 });
  private static authRateLimiter = new RateLimiter({ windowMs: 60000, maxRequests: 10 });
  private static deviceRateLimiter = new RateLimiter({ windowMs: 300000, maxRequests: 3 });
  
  private static suspiciousIPs = new Set<string>();
  private static blockedFingerprints = new Set<string>();

  static async setupSecurityMiddleware(fastify: FastifyInstance): Promise<void> {
    fastify.addHook('preHandler', async (request: FastifyRequest, reply: FastifyReply) => {
      const securityCheck = await this.performSecurityCheck(request);
      
      if (!securityCheck.isValid) {
        await this.logSecurityViolation(request, securityCheck);
        throw new AuthenticationError('Security check failed');
      }

      if (securityCheck.riskScore > 50) {
        logger.warn(`High risk request detected: ${securityCheck.riskScore}`, {
          ip: request.ip,
          userAgent: request.headers['user-agent'],
          reasons: securityCheck.reasons
        });
      }
    });

    fastify.addHook('onRequest', async (request: FastifyRequest, reply: FastifyReply) => {
      if (!request.headers['user-agent']) {
        throw new ValidationError('User-Agent header is required');
      }

      const httpsOnly = process.env.NODE_ENV === 'production';
      if (httpsOnly && request.protocol !== 'https') {
        throw new ValidationError('HTTPS is required');
      }
    });
  }

  static async rateLimitQR(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const identifier = this.getClientIdentifier(request);
    const result = await this.qrRateLimiter.checkLimit(identifier);

    if (!result.allowed) {
      reply.header('X-RateLimit-Limit', '5');
      reply.header('X-RateLimit-Remaining', '0');
      reply.header('X-RateLimit-Reset', result.resetTime.toString());
      
      await this.logSecurityViolation(request, {
        isValid: false,
        riskScore: 80,
        reasons: ['QR rate limit exceeded']
      });

      throw new ValidationError('QR generation rate limit exceeded');
    }

    reply.header('X-RateLimit-Limit', '5');
    reply.header('X-RateLimit-Remaining', result.remaining.toString());
    reply.header('X-RateLimit-Reset', result.resetTime.toString());
  }

  static async rateLimitAuth(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const identifier = this.getClientIdentifier(request);
    const result = await this.authRateLimiter.checkLimit(identifier);

    if (!result.allowed) {
      reply.header('X-RateLimit-Limit', '10');
      reply.header('X-RateLimit-Remaining', '0');
      reply.header('X-RateLimit-Reset', result.resetTime.toString());
      
      await this.logSecurityViolation(request, {
        isValid: false,
        riskScore: 70,
        reasons: ['Auth rate limit exceeded']
      });

      throw new ValidationError('Authentication rate limit exceeded');
    }

    reply.header('X-RateLimit-Limit', '10');
    reply.header('X-RateLimit-Remaining', result.remaining.toString());
    reply.header('X-RateLimit-Reset', result.resetTime.toString());
  }

  static async rateLimitDevice(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const identifier = this.getClientIdentifier(request);
    const result = await this.deviceRateLimiter.checkLimit(identifier);

    if (!result.allowed) {
      reply.header('X-RateLimit-Limit', '3');
      reply.header('X-RateLimit-Remaining', '0');
      reply.header('X-RateLimit-Reset', result.resetTime.toString());
      
      await this.logSecurityViolation(request, {
        isValid: false,
        riskScore: 90,
        reasons: ['Device linking rate limit exceeded']
      });

      throw new ValidationError('Device linking rate limit exceeded');
    }

    reply.header('X-RateLimit-Limit', '3');
    reply.header('X-RateLimit-Remaining', result.remaining.toString());
    reply.header('X-RateLimit-Reset', result.resetTime.toString());
  }

  static async validateDeviceFingerprint(request: FastifyRequest): Promise<void> {
    const fingerprint = request.headers['x-device-fingerprint'] as string;
    
    if (!fingerprint) {
      throw new ValidationError('Device fingerprint is required');
    }

    if (this.blockedFingerprints.has(fingerprint)) {
      await this.logSecurityViolation(request, {
        isValid: false,
        riskScore: 100,
        reasons: ['Blocked device fingerprint']
      });
      throw new AuthenticationError('Device is blocked');
    }

    const expectedFingerprint = this.generateExpectedFingerprint(request);
    if (!this.isValidFingerprint(fingerprint, expectedFingerprint)) {
      await this.logSecurityViolation(request, {
        isValid: false,
        riskScore: 85,
        reasons: ['Invalid device fingerprint']
      });
      throw new ValidationError('Invalid device fingerprint');
    }
  }

  static async validateIPAddress(request: FastifyRequest): Promise<void> {
    const clientIP = request.ip;
    
    if (this.suspiciousIPs.has(clientIP)) {
      await this.logSecurityViolation(request, {
        isValid: false,
        riskScore: 95,
        reasons: ['Suspicious IP address']
      });
      throw new AuthenticationError('Access denied from this IP address');
    }

    if (await this.isIPBlacklisted(clientIP)) {
      this.suspiciousIPs.add(clientIP);
      await this.logSecurityViolation(request, {
        isValid: false,
        riskScore: 100,
        reasons: ['Blacklisted IP address']
      });
      throw new AuthenticationError('Access denied from this IP address');
    }
  }

  static async requireUserConfirmation(
    userPhone: string,
    deviceInfo: any,
    action: string
  ): Promise<{ confirmationRequired: boolean; confirmationCode?: string }> {
    const riskScore = await this.calculateActionRiskScore(userPhone, deviceInfo, action);
    
    if (riskScore > 60) {
      const confirmationCode = this.generateConfirmationCode();
      
      await this.logSecurityEvent('confirmation_required', {
        user_phone: userPhone,
        action,
        risk_score: riskScore,
        device_info: deviceInfo,
        confirmation_code: confirmationCode
      });

      return {
        confirmationRequired: true,
        confirmationCode
      };
    }

    return { confirmationRequired: false };
  }

  private static async performSecurityCheck(request: FastifyRequest): Promise<SecurityCheck> {
    let riskScore = 0;
    const reasons: string[] = [];

    const clientIP = request.ip;
    if (this.suspiciousIPs.has(clientIP)) {
      riskScore += 50;
      reasons.push('Suspicious IP address');
    }

    const userAgent = request.headers['user-agent'] || '';
    if (this.isSuspiciousUserAgent(userAgent)) {
      riskScore += 30;
      reasons.push('Suspicious user agent');
    }

    const fingerprint = request.headers['x-device-fingerprint'] as string;
    if (fingerprint && this.blockedFingerprints.has(fingerprint)) {
      riskScore += 100;
      reasons.push('Blocked device fingerprint');
    }

    if (await this.hasRecentSecurityViolations(clientIP)) {
      riskScore += 40;
      reasons.push('Recent security violations');
    }

    const isValid = riskScore < 100;

    return { isValid, riskScore, reasons };
  }

  private static getClientIdentifier(request: FastifyRequest): string {
    const ip = request.ip;
    const userAgent = request.headers['user-agent'] || '';
    const fingerprint = request.headers['x-device-fingerprint'] || '';
    
    return `${ip}:${userAgent}:${fingerprint}`;
  }

  private static generateExpectedFingerprint(request: FastifyRequest): string {
    const data = {
      userAgent: request.headers['user-agent'],
      acceptLanguage: request.headers['accept-language'],
      acceptEncoding: request.headers['accept-encoding']
    };
    
    return CryptoService.generateDeviceFingerprint(
      Buffer.from(JSON.stringify(data)),
      { timestamp: Date.now() }
    );
  }

  private static isValidFingerprint(provided: string, expected: string): boolean {
    return provided.length === expected.length && provided.length >= 32;
  }

  private static async isIPBlacklisted(ip: string): Promise<boolean> {
    const knownBadIPs = ['127.0.0.1', '0.0.0.0'];
    return knownBadIPs.includes(ip);
  }

  private static isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /curl/i,
      /wget/i,
      /python/i
    ];
    
    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  private static async hasRecentSecurityViolations(ip: string): Promise<boolean> {
    const recentDate = new Date(Date.now() - 60 * 60 * 1000); // Last hour
    
    const violations = await AuditLog.countDocuments({
      ip_address: ip,
      event_type: 'security_violation',
      created_at: { $gte: recentDate }
    });

    return violations > 3;
  }

  private static async calculateActionRiskScore(
    userPhone: string,
    deviceInfo: any,
    action: string
  ): Promise<number> {
    let riskScore = 0;

    if (action === 'device_link') {
      riskScore += 40;
    }

    if (deviceInfo.deviceType === 'web') {
      riskScore += 20;
    }

    const recentDeviceLinks = await AuditLog.countDocuments({
      user_phone: userPhone,
      event_type: 'device_linked',
      created_at: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });

    if (recentDeviceLinks > 0) {
      riskScore += 30;
    }

    return riskScore;
  }

  private static generateConfirmationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private static async logSecurityViolation(request: FastifyRequest, securityCheck: SecurityCheck): Promise<void> {
    await this.logSecurityEvent('security_violation', {
      ip_address: request.ip,
      user_agent: request.headers['user-agent'] || '',
      risk_score: securityCheck.riskScore,
      reasons: securityCheck.reasons,
      url: request.url,
      method: request.method
    });
  }

  private static async logSecurityEvent(eventType: string, data: any): Promise<void> {
    try {
      const auditLog = new AuditLog({
        event_type: eventType,
        user_phone: data.user_phone,
        device_id: data.device_id,
        ip_address: data.ip_address,
        user_agent: data.user_agent,
        event_data: data,
        risk_score: data.risk_score
      });
      await auditLog.save();
    } catch (error) {
      logger.error('Failed to log security event:', error);
    }
  }
}
