import { FastifyInstance, FastifyPluginOptions, FastifyRequest, FastifyReply } from 'fastify';
import * as twilioService from '../services/twilio.service';
import { ValidationError } from '../utils/errors';
import { logger } from '../utils/logger';

interface TestSmsRequest {
  phone: string;
}

export async function systemRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  
  fastify.get('/sms-status', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const status = twilioService.getServiceStatus();
      
      return reply.status(200).send({
        success: true,
        message: 'SMS service status retrieved',
        data: {
          sms_service: status,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error: any) {
      logger.error('Error getting SMS status:', error);
      return reply.status(500).send({
        success: false,
        message: 'Failed to get SMS service status'
      });
    }
  });

  fastify.post('/test-sms', async (request: FastifyRequest<{ Body: TestSmsRequest }>, reply: FastifyReply) => {
    try {
      if (!request.body || !request.body.phone) {
        throw new ValidationError('Phone number is required');
      }

      const { phone } = request.body;
      logger.info(`Testing SMS service with phone: ${phone}`);

      const result = await twilioService.testSmsService(phone);
      
      return reply.status(result.success ? 200 : 400).send({
        success: result.success,
        message: result.message,
        data: result.details
      });
    } catch (error: any) {
      if (error instanceof ValidationError) {
        return reply.status(400).send({
          success: false,
          message: error.message
        });
      }
      
      logger.error('Error testing SMS service:', error);
      return reply.status(500).send({
        success: false,
        message: 'SMS test failed'
      });
    }
  });

  fastify.get('/health', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const smsStatus = twilioService.getServiceStatus();
      
      return reply.status(200).send({
        success: true,
        message: 'System health check',
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          services: {
            sms: {
              enabled: smsStatus.enabled,
              configured: smsStatus.configured
            }
          },
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: process.version
        }
      });
    } catch (error: any) {
      logger.error('Health check error:', error);
      return reply.status(500).send({
        success: false,
        message: 'Health check failed'
      });
    }
  });
}
