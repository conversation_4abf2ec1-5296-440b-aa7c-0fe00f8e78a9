import { FastifyRequest, FastifyReply } from 'fastify';
import * as contactService from '../services/contact.service';
import { ValidationError, NotFoundError } from '../utils/errors';
import { logger } from '../utils/logger';
import { PaginationUtil } from '../utils/pagination.util';
import {
  ApiResponse,
  BulkContactsRequest,
  AuthenticatedRequest
} from '../types';

export const uploadContacts = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { contacts } = request.body as BulkContactsRequest;

  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  if (!contacts || contacts.length === 0) {
    return reply.status(400).send({
      success: false,
      message: 'No contacts provided'
    });
  }

  // No limit on contact upload - chunking will be handled in the service layer
  logger.info(`Processing ${contacts.length} contacts for user ${request.user.phone}`);

  try {
    const userId = request.user.userId || request.user.phone;
    const response = await contactService.uploadContacts(userId, contacts);

    logger.info(`Contacts uploaded for user ${userId}: ${response.contacts_processed} contacts processed, ${response.contacts_registered} registered`);

    return reply.status(200).send({
      success: true,
      message: response.message,
      data: response
    });
  } catch (error: any) {
    logger.error('Error uploading contacts:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to upload contacts'
    });
  }
};

export const getContacts = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const query = request.query as any;

    const searchTerm = query?.search;
    const registeredOnly = query?.registered === 'true';
    const showAll = query?.all === 'true';

    const endpointType = showAll ? 'contacts-all' : 'contacts';
    const paginationQuery = PaginationUtil.parsePaginationQuery(query, endpointType);

    PaginationUtil.validatePaginationSecurity(paginationQuery.page, paginationQuery.limit, endpointType);

    if (PaginationUtil.shouldRateLimit(paginationQuery.page, paginationQuery.limit, endpointType)) {
      return reply.status(429).send({
        success: false,
        message: 'Request rate limited. Please use smaller page sizes or cursor-based pagination.'
      });
    }

    const result = await contactService.getUserContacts(userId, paginationQuery, searchTerm, registeredOnly, showAll);

    return reply.status(200).send(result);
  } catch (error: any) {
    logger.error('Error getting contacts:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve contacts'
    });
  }
};

export const syncContacts = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const response = await contactService.getUserContactsLegacy(userId);

    logger.info(`Contacts synced for user ${userId}: ${response.total_contacts} total, ${response.registered_contacts} registered`);

    return reply.status(200).send({
      success: true,
      message: 'Contacts synced successfully',
      data: response
    });
  } catch (error: any) {
    logger.error('Error syncing contacts:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to sync contacts'
    });
  }
};
