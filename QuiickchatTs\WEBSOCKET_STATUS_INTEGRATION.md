# WebSocket Status Integration Guide

## Overview
This guide explains how to integrate real-time status updates using WebSockets for intelligent, dynamic status management in your frontend application.

## WebSocket Connection Setup

### 1. Connect to WebSocket
```javascript
const ws = new WebSocket('ws://localhost:3000/status-updates');

// Authentication
ws.onopen = () => {
  ws.send(JSON.stringify({
    type: 'auth',
    token: localStorage.getItem('access_token')
  }));
};
```

### 2. Handle Authentication Response
```javascript
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  switch (data.type) {
    case 'auth_success':
      console.log('WebSocket authenticated successfully');
      // Subscribe to contact status updates
      subscribeToContacts();
      // Request initial status data
      requestInitialStatusData();
      break;
      
    case 'error':
      console.error('WebSocket error:', data.message);
      break;
  }
};
```

## Intelligent Status Management

### 3. Subscribe to Contact Status Updates
```javascript
function subscribeToContacts() {
  // Get user's contacts and subscribe to their status updates
  const contacts = getUserContacts(); // Your method to get contacts
  
  contacts.forEach(contact => {
    ws.send(JSON.stringify({
      type: 'subscribe',
      contact_phone: contact.phone
    }));
  });
}
```

### 4. Request Initial Status Data
```javascript
function requestInitialStatusData() {
  // Request recent status updates
  ws.send(JSON.stringify({
    type: 'request_status_updates'
  }));
  
  // Request contact status summaries
  ws.send(JSON.stringify({
    type: 'request_contact_statuses'
  }));
}
```

## Real-time Status Updates

### 5. Handle Incoming Status Updates
```javascript
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  switch (data.type) {
    case 'status_update':
      handleNewStatusUpdate(data);
      break;
      
    case 'status_updates_batch':
      handleBatchStatusUpdates(data.statuses);
      break;
      
    case 'contact_statuses':
      handleContactStatusSummaries(data.summaries);
      break;
      
    case 'status_viewed':
      handleStatusViewed(data);
      break;
      
    case 'status_deleted':
      handleStatusDeleted(data);
      break;
  }
};
```

### 6. Intelligent Status Sorting
```javascript
function handleNewStatusUpdate(data) {
  const statusData = data.data;
  const userPhone = data.user_phone;
  
  // Add to status list with intelligent positioning
  const statusList = getStatusList(); // Your status list
  
  // Sort based on priority, interaction score, and recency
  const insertIndex = findOptimalInsertPosition(statusData, statusList);
  
  statusList.splice(insertIndex, 0, {
    ...statusData,
    user_phone: userPhone,
    timestamp: data.timestamp
  });
  
  // Update UI
  updateStatusUI(statusList);
  
  // Show notification if needed
  if (statusData.is_recent && statusData.priority > 5) {
    showStatusNotification(userPhone, statusData);
  }
}

function findOptimalInsertPosition(newStatus, statusList) {
  // Intelligent sorting algorithm
  for (let i = 0; i < statusList.length; i++) {
    const currentStatus = statusList[i];
    
    // Priority-based sorting
    if (newStatus.priority > currentStatus.priority) {
      return i;
    }
    
    // If same priority, sort by recency
    if (newStatus.priority === currentStatus.priority) {
      if (new Date(newStatus.created_at) > new Date(currentStatus.created_at)) {
        return i;
      }
    }
  }
  
  return statusList.length; // Insert at end
}
```

### 7. Dynamic Contact Status Management
```javascript
function handleContactStatusSummaries(summaries) {
  // Sort contacts by status activity
  const sortedContacts = summaries.sort((a, b) => {
    // Prioritize contacts with unviewed statuses
    if (a.unviewed_statuses !== b.unviewed_statuses) {
      return b.unviewed_statuses - a.unviewed_statuses;
    }
    
    // Then by recent activity
    if (a.has_recent_activity !== b.has_recent_activity) {
      return a.has_recent_activity ? -1 : 1;
    }
    
    // Finally by latest status time
    return new Date(b.latest_status_time) - new Date(a.latest_status_time);
  });
  
  // Update contact list UI
  updateContactStatusList(sortedContacts);
}

function updateContactStatusList(contacts) {
  const contactContainer = document.getElementById('contact-status-list');
  
  contacts.forEach(contact => {
    const contactElement = createContactStatusElement(contact);
    contactContainer.appendChild(contactElement);
  });
}
```

## Status Interaction Tracking

### 8. Track Status Interactions
```javascript
function viewStatus(statusId, ownerPhone) {
  // Mark status as viewed
  ws.send(JSON.stringify({
    type: 'mark_status_viewed',
    status_id: statusId,
    owner_phone: ownerPhone
  }));
  
  // Track interaction for intelligent sorting
  ws.send(JSON.stringify({
    type: 'status_interaction',
    status_id: statusId,
    interaction_type: 'view',
    owner_phone: ownerPhone
  }));
  
  // Update local UI
  markStatusAsViewed(statusId);
}

function likeStatus(statusId, ownerPhone) {
  // Track like interaction
  ws.send(JSON.stringify({
    type: 'status_interaction',
    status_id: statusId,
    interaction_type: 'like',
    owner_phone: ownerPhone
  }));
}

function shareStatus(statusId, ownerPhone) {
  // Track share interaction
  ws.send(JSON.stringify({
    type: 'status_interaction',
    status_id: statusId,
    interaction_type: 'share',
    owner_phone: ownerPhone
  }));
}
```

## Advanced Features

### 9. Smart Notifications
```javascript
function showStatusNotification(userPhone, statusData) {
  // Only show notifications for high-priority statuses
  if (statusData.priority < 7) return;
  
  const contact = getContactByPhone(userPhone);
  const notificationTitle = `${contact.name} posted a new status`;
  
  let notificationBody = '';
  switch (statusData.content_type) {
    case 'text':
      notificationBody = statusData.content || statusData.caption;
      break;
    case 'image':
      notificationBody = '📷 Shared a photo';
      break;
    case 'video':
      notificationBody = '🎥 Shared a video';
      break;
  }
  
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification(notificationTitle, {
      body: notificationBody,
      icon: contact.profile_picture || '/default-avatar.png',
      tag: `status-${statusData.id}`,
      data: { statusId: statusData.id, userPhone }
    });
  }
}
```

### 10. Offline Status Caching
```javascript
class StatusCache {
  constructor() {
    this.cache = new Map();
    this.maxCacheSize = 100;
  }
  
  addStatus(status) {
    // Add to cache with timestamp
    this.cache.set(status.id, {
      ...status,
      cached_at: Date.now()
    });
    
    // Cleanup old entries
    if (this.cache.size > this.maxCacheSize) {
      this.cleanupOldEntries();
    }
    
    // Store in localStorage for persistence
    this.persistCache();
  }
  
  getStatus(statusId) {
    return this.cache.get(statusId);
  }
  
  cleanupOldEntries() {
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].cached_at - b[1].cached_at);
    
    // Remove oldest 20% of entries
    const removeCount = Math.floor(entries.length * 0.2);
    for (let i = 0; i < removeCount; i++) {
      this.cache.delete(entries[i][0]);
    }
  }
  
  persistCache() {
    const cacheData = Array.from(this.cache.entries());
    localStorage.setItem('status_cache', JSON.stringify(cacheData));
  }
  
  loadCache() {
    const cacheData = localStorage.getItem('status_cache');
    if (cacheData) {
      const entries = JSON.parse(cacheData);
      this.cache = new Map(entries);
    }
  }
}

const statusCache = new StatusCache();
statusCache.loadCache();
```

## React/Vue Integration Examples

### 11. React Hook for Status Management
```javascript
import { useState, useEffect, useRef } from 'react';

export function useStatusWebSocket(accessToken) {
  const [statuses, setStatuses] = useState([]);
  const [contactSummaries, setContactSummaries] = useState([]);
  const [isConnected, setIsConnected] = useState(false);
  const wsRef = useRef(null);
  
  useEffect(() => {
    if (!accessToken) return;
    
    const ws = new WebSocket('ws://localhost:3000/status-updates');
    wsRef.current = ws;
    
    ws.onopen = () => {
      ws.send(JSON.stringify({ type: 'auth', token: accessToken }));
    };
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      switch (data.type) {
        case 'auth_success':
          setIsConnected(true);
          ws.send(JSON.stringify({ type: 'request_status_updates' }));
          ws.send(JSON.stringify({ type: 'request_contact_statuses' }));
          break;
          
        case 'status_update':
          setStatuses(prev => {
            const newStatuses = [...prev];
            const insertIndex = findOptimalInsertPosition(data.data, newStatuses);
            newStatuses.splice(insertIndex, 0, data.data);
            return newStatuses;
          });
          break;
          
        case 'contact_statuses':
          setContactSummaries(data.summaries);
          break;
      }
    };
    
    ws.onclose = () => {
      setIsConnected(false);
    };
    
    return () => {
      ws.close();
    };
  }, [accessToken]);
  
  const viewStatus = (statusId, ownerPhone) => {
    if (wsRef.current) {
      wsRef.current.send(JSON.stringify({
        type: 'mark_status_viewed',
        status_id: statusId,
        owner_phone: ownerPhone
      }));
    }
  };
  
  return {
    statuses,
    contactSummaries,
    isConnected,
    viewStatus
  };
}
```

### 12. Vue Composition API
```javascript
import { ref, onMounted, onUnmounted } from 'vue';

export function useStatusWebSocket(accessToken) {
  const statuses = ref([]);
  const contactSummaries = ref([]);
  const isConnected = ref(false);
  let ws = null;
  
  onMounted(() => {
    if (!accessToken.value) return;
    
    ws = new WebSocket('ws://localhost:3000/status-updates');
    
    ws.onopen = () => {
      ws.send(JSON.stringify({ type: 'auth', token: accessToken.value }));
    };
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      handleWebSocketMessage(data);
    };
    
    ws.onclose = () => {
      isConnected.value = false;
    };
  });
  
  onUnmounted(() => {
    if (ws) {
      ws.close();
    }
  });
  
  function handleWebSocketMessage(data) {
    switch (data.type) {
      case 'auth_success':
        isConnected.value = true;
        requestInitialData();
        break;
        
      case 'status_update':
        addStatusIntelligently(data.data);
        break;
        
      case 'contact_statuses':
        contactSummaries.value = data.summaries;
        break;
    }
  }
  
  return {
    statuses,
    contactSummaries,
    isConnected
  };
}
```

## Best Practices

1. **Connection Management**: Always handle reconnection logic for network interruptions
2. **Memory Management**: Implement proper cleanup for old statuses and cache management
3. **Performance**: Use virtual scrolling for large status lists
4. **User Experience**: Show loading states and offline indicators
5. **Privacy**: Respect user privacy settings and implement proper filtering
6. **Notifications**: Implement smart notification logic to avoid spam
7. **Error Handling**: Gracefully handle WebSocket errors and fallback to polling if needed

This integration provides real-time, intelligent status updates that automatically sort and prioritize content based on user interactions and relationships.
