import * as libsignal from '@signalapp/libsignal-client';
import sodium from 'sodium-native';
import { v4 as uuidv4 } from 'uuid';
import { DeviceLinkSession, AuditLog } from '../models/device-link.model';
import { User, IDeviceRecord } from '../models/user.model';
import { logger } from '../utils/logger';
import { ValidationError, NotFoundError, AuthenticationError, ConflictError } from '../utils/errors';
import { generateVerificationCode } from '../utils/helpers';
import { CryptoService, KeyPair } from './crypto.service';

interface DeviceLinkRequest {
  userPhone: string;
  primaryDeviceId: string;
  deviceData: {
    deviceName: string;
    deviceType: 'ios' | 'android' | 'web' | 'desktop';
    deviceFingerprint: string;
    ipAddress: string;
    userAgent: string;
    appVersion?: string | undefined;
    osVersion?: string | undefined;
  };
  cryptoData: {
    identityKey: string;
    signedPreKey: string;
    preKeyBundle: string;
    registrationId: string;
  };
}

interface X3DHBundle {
  identityKey: Uint8Array;
  signedPreKey: Uint8Array;
  preKeySignature: Uint8Array;
  oneTimePreKey?: Uint8Array;
}

const LINK_EXPIRY_MINUTES = 10;
const MAX_VERIFICATION_ATTEMPTS = 3;
const MAX_DEVICES_PER_USER = 5;

export const initiateLinking = async (request: DeviceLinkRequest): Promise<{ sessionId: string; verificationCode: string; expiresAt: Date }> => {
    const user = await User.findOne({ phone: request.userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    if (user.devices.length >= MAX_DEVICES_PER_USER) {
      throw new ValidationError(`Maximum of ${MAX_DEVICES_PER_USER} devices allowed per user`);
    }

    const existingDevice = user.devices.find(d => d.device_fingerprint === request.deviceData.deviceFingerprint);
    if (existingDevice) {
      throw new ConflictError('Device already linked to this account');
    }

  const sessionId = generateSecureSessionId();
  const verificationCode = generateVerificationCode();
  const challenge = generateChallenge();
  const expiresAt = new Date(Date.now() + LINK_EXPIRY_MINUTES * 60 * 1000);

  const x3dhBundle = await generateX3DHBundle(request.cryptoData);
  const ephemeralKey = generateEphemeralKey();

    const session = new DeviceLinkSession({
      session_id: sessionId,
      user_phone: request.userPhone,
      primary_device_id: request.primaryDeviceId,
      linking_device_data: {
        device_name: request.deviceData.deviceName,
        device_type: request.deviceData.deviceType,
        device_fingerprint: request.deviceData.deviceFingerprint,
        identity_key: request.cryptoData.identityKey,
        signed_pre_key: request.cryptoData.signedPreKey,
        pre_key_bundle: request.cryptoData.preKeyBundle,
        registration_id: request.cryptoData.registrationId,
        ip_address: request.deviceData.ipAddress,
        user_agent: request.deviceData.userAgent,
        app_version: request.deviceData.appVersion,
        os_version: request.deviceData.osVersion
      },
      handshake_data: {
        x3dh_bundle: JSON.stringify(x3dhBundle),
        ephemeral_key: ephemeralKey
      },
      verification_data: {
        challenge,
        verification_code: verificationCode,
        attempts: 0
      },
      expires_at: expiresAt
    });

    await session.save();

  await logAuditEvent('device_link_initiated', {
      session_id: sessionId,
      user_phone: request.userPhone,
      device_id: request.deviceData.deviceFingerprint,
      ip_address: request.deviceData.ipAddress,
      user_agent: request.deviceData.userAgent,
      event_data: {
        device_name: request.deviceData.deviceName,
        device_type: request.deviceData.deviceType,
        primary_device_id: request.primaryDeviceId
      }
    });

    logger.info(`Device linking initiated for user ${request.userPhone}, session: ${sessionId}`);

  return { sessionId, verificationCode, expiresAt };
};

export const verifyLinking = async (sessionId: string, verificationCode: string, userPhone: string): Promise<{ success: boolean; sharedSecret?: string }> => {
    const session = await DeviceLinkSession.findOne({
      session_id: sessionId,
      user_phone: userPhone,
      status: 'pending'
    });

    if (!session) {
      throw new NotFoundError('Device linking session not found');
    }

    if (session.expires_at < new Date()) {
      session.status = 'expired';
      await session.save();
      throw new ValidationError('Device linking session has expired');
    }

  if (session.verification_data.attempts >= MAX_VERIFICATION_ATTEMPTS) {
      session.status = 'failed';
      await session.save();
      throw new ValidationError('Maximum verification attempts exceeded');
    }

    if (session.verification_data.verification_code !== verificationCode) {
      session.verification_data.attempts += 1;
      await session.save();
      throw new AuthenticationError('Invalid verification code');
    }

  const sharedSecret = await performX3DHHandshake(session);

    session.status = 'verified';
    session.verified_at = new Date();
    session.handshake_data.shared_secret = sharedSecret;
    await session.save();

    logger.info(`Device linking verified for session ${sessionId}`);

  return { success: true, sharedSecret };
};

export const completeLinking = async (sessionId: string, userPhone: string): Promise<{ deviceId: string; success: boolean }> => {
    const session = await DeviceLinkSession.findOne({
      session_id: sessionId,
      user_phone: userPhone,
      status: 'verified'
    });

    if (!session) {
      throw new NotFoundError('Device linking session not found or not verified');
    }

    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const deviceId = uuidv4();
    const newDevice: IDeviceRecord = {
      device_id: deviceId,
      device_name: session.linking_device_data.device_name,
      device_type: session.linking_device_data.device_type,
      device_fingerprint: session.linking_device_data.device_fingerprint,
      identity_key: session.linking_device_data.identity_key,
      signed_pre_key: session.linking_device_data.signed_pre_key,
      pre_key_bundle: session.linking_device_data.pre_key_bundle,
      registration_id: session.linking_device_data.registration_id,
      is_primary: false,
      is_active: true,
      last_seen: new Date(),
      ip_address: session.linking_device_data.ip_address,
      user_agent: session.linking_device_data.user_agent,
      app_version: session.linking_device_data.app_version,
      os_version: session.linking_device_data.os_version,
      linked_at: new Date(),
      verified_at: new Date()
    };

    user.devices.push(newDevice);
    await user.save();

    session.status = 'completed';
    session.completed_at = new Date();
    await session.save();

  await logAuditEvent('device_linked', {
      session_id: sessionId,
      user_phone: userPhone,
      device_id: deviceId,
      ip_address: session.linking_device_data.ip_address,
      user_agent: session.linking_device_data.user_agent,
      event_data: {
        device_name: session.linking_device_data.device_name,
        device_type: session.linking_device_data.device_type,
        device_fingerprint: session.linking_device_data.device_fingerprint
      }
    });

    logger.info(`Device linking completed for user ${userPhone}, new device: ${deviceId}`);

  return { deviceId, success: true };
};

export const revokeDevice = async (userPhone: string, deviceId: string, revokerDeviceId: string): Promise<{ success: boolean }> => {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const deviceIndex = user.devices.findIndex(d => d.device_id === deviceId);
    if (deviceIndex === -1) {
      throw new NotFoundError('Device not found');
    }

    const device = user.devices[deviceIndex];
    if (!device) {
      throw new NotFoundError('Device not found');
    }

    if (device.is_primary) {
      throw new ValidationError('Cannot revoke primary device');
    }

    user.devices.splice(deviceIndex, 1);
    await user.save();

  await logAuditEvent('device_revoked', {
      user_phone: userPhone,
      device_id: deviceId,
      ip_address: '',
      user_agent: '',
      event_data: {
        revoked_by: revokerDeviceId,
        device_name: device.device_name,
        device_type: device.device_type
      }
    });

    logger.info(`Device ${deviceId} revoked for user ${userPhone} by ${revokerDeviceId}`);

  return { success: true };
};

export const listDevices = async (userPhone: string): Promise<IDeviceRecord[]> => {
    const user = await User.findOne({ phone: userPhone });
    if (!user) {
      throw new NotFoundError('User not found');
    }

  return user.devices.filter(d => d.is_active);
};

const generateX3DHBundle = async (cryptoData: any): Promise<X3DHBundle> => {
    try {
      const identityKey = Buffer.from(cryptoData.identityKey, 'base64');
      const signedPreKey = Buffer.from(cryptoData.signedPreKey, 'base64');

      const identityKeyPair = CryptoService.generateIdentityKeyPair();
      const signature = CryptoService.verifySignature(identityKey, signedPreKey, Buffer.alloc(64));

      return {
        identityKey: new Uint8Array(identityKey),
        signedPreKey: new Uint8Array(signedPreKey),
        preKeySignature: new Uint8Array(Buffer.alloc(64))
      };
    } catch (error) {
      logger.error('Failed to generate X3DH bundle:', error);
      throw new ValidationError('Invalid cryptographic data');
  }
};

const generateEphemeralKey = (): string => {
  const keyPair = CryptoService.generateIdentityKeyPair();
  return Buffer.concat([keyPair.publicKey, keyPair.privateKey]).toString('base64');
};

const performX3DHHandshake = async (session: any): Promise<string> => {
    try {
      const ephemeralKeyData = Buffer.from(session.handshake_data.ephemeral_key, 'base64');
      const ephemeralKeyPair: KeyPair = {
        publicKey: ephemeralKeyData.subarray(0, 32),
        privateKey: ephemeralKeyData.subarray(32, 64)
      };

      const identityKey = Buffer.from(session.linking_device_data.identity_key, 'base64');
      const signedPreKey = Buffer.from(session.linking_device_data.signed_pre_key, 'base64');

      const identityKeyPair = CryptoService.generateIdentityKeyPair();

      const x3dhResult = CryptoService.performX3DH(
        identityKeyPair,
        ephemeralKeyPair,
        identityKey,
        signedPreKey
      );

      const rootKey = CryptoService.deriveRootKey(x3dhResult.sharedSecret);

      session.handshake_data.root_key = Buffer.from(rootKey).toString('base64');

      return Buffer.from(x3dhResult.sharedSecret).toString('base64');
    } catch (error) {
      logger.error('X3DH handshake failed:', error);
      throw new ValidationError('Cryptographic handshake failed');
  }
};

const generateSecureSessionId = (): string => {
  const buffer = Buffer.allocUnsafe(32);
  sodium.randombytes_buf(buffer);
  return buffer.toString('hex');
};

const generateChallenge = (): string => {
  const buffer = Buffer.allocUnsafe(32);
  sodium.randombytes_buf(buffer);
  return buffer.toString('base64');
};

const logAuditEvent = async (eventType: string, data: any): Promise<void> => {
  try {
    const auditLog = new AuditLog({
      event_type: eventType,
      user_phone: data.user_phone,
      device_id: data.device_id,
      session_id: data.session_id,
      ip_address: data.ip_address,
      user_agent: data.user_agent,
      event_data: data.event_data || {}
    });
    await auditLog.save();
  } catch (error) {
    logger.error('Failed to log audit event:', error);
  }
};
