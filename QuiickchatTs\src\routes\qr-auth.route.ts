import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import {
  generateQRForAuth,
  scanQR<PERSON><PERSON><PERSON><PERSON>,
  approveQRAuth,
  rejectQRAuth,
  checkQRStatus,
  initiateDeviceLink,
  verifyDeviceLink,
  completeDeviceLink,
  revokeLinkedDevice,
  listLinkedDevices
} from '../controllers/qr-auth.controller';
import { authenticateRequest } from '../middleware/auth.middleware';

export async function qrAuthRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // QR Code Generation (Public - for login/device linking initiation)
  fastify.post('/generate', generateQRForAuth as any);

  // QR Code Status Check (Public - for polling status)
  fastify.get('/status/:sessionId', checkQRStatus as any);

  // QR Code Scanning (Authenticated - mobile app scans QR)
  fastify.post('/scan', { preHandler: authenticateRequest }, scanQRForAuth as any);

  // QR Code Approval (Authenticated - mobile app approves login/linking)
  fastify.post('/approve', { preHandler: authenticateRequest }, approveQRAuth as any);

  // QR Code Rejection (Authenticated - mobile app rejects login/linking)
  fastify.post('/reject', { preHandler: authenticateRequest }, rejectQRAuth as any);

  // Device Linking Endpoints
  fastify.post('/device/initiate', { preHandler: authenticateRequest }, initiateDeviceLink as any);
  fastify.post('/device/verify', { preHandler: authenticateRequest }, verifyDeviceLink as any);
  fastify.post('/device/complete', { preHandler: authenticateRequest }, completeDeviceLink as any);
  fastify.post('/device/revoke', { preHandler: authenticateRequest }, revokeLinkedDevice as any);
  fastify.get('/devices/list', { preHandler: authenticateRequest }, listLinkedDevices as any);
}
