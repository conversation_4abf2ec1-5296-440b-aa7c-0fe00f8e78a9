import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import {
  generateDeepLink,
  parseDeepLink,
  handleQRScanRedirect,
  handleDeviceLinkRedirect,
  getAppStoreLinks,
  generateSmartBanner,
  validateDeepLinkSession
} from '../controllers/deep-link.controller';
import { authenticateRequest } from '../middleware/auth.middleware';

export async function deepLinkRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // Deep Link Generation (Authenticated)
  fastify.post('/generate', { preHandler: authenticateRequest }, generateDeepLink as any);

  // Deep Link Parsing (Authenticated)
  fastify.post('/parse', { preHandler: authenticateRequest }, parseDeepLink as any);

  // Public redirect handlers for universal links
  fastify.get('/qr-scan/:sessionId', handleQRScanRedirect as any);
  fastify.get('/device-link/:sessionId', handleDeviceLinkRedirect as any);

  // App store links (Public)
  fastify.get('/app-store', getAppStoreLinks as any);

  // Smart banner generation (Public)
  fastify.get('/smart-banner/:sessionId/:action', generateSmartBanner as any);

  // Session validation (Public - for mobile apps)
  fastify.get('/validate/:sessionId/:action', validateDeepLinkSession as any);
}
