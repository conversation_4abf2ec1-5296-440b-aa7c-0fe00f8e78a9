import { FastifyRequest, FastifyReply } from 'fastify';
import * as qrAuthService from '../services/qr-auth.service';
import * as deviceLinkService from '../services/device-link.service';
import { ValidationError, NotFoundError, AuthenticationError } from '../utils/errors';
import { logger } from '../utils/logger';
import { ApiResponse, AuthenticatedRequest } from '../types';

interface GenerateQRRequest {
  session_type: 'login' | 'device_link';
  device_fingerprint: string;
}

interface ScanQRRequest {
  qr_data: string;
}

interface ApproveQRRequest {
  session_id: string;
  device_name: string;
  device_type: 'ios' | 'android' | 'web' | 'desktop';
  identity_key: string;
  signed_pre_key: string;
  pre_key_bundle: string;
  registration_id: string;
  device_fingerprint: string;
}

interface RejectQRRequest {
  session_id: string;
}

interface InitiateLinkRequest {
  device_name: string;
  device_type: 'ios' | 'android' | 'web' | 'desktop';
  device_fingerprint: string;
  identity_key: string;
  signed_pre_key: string;
  pre_key_bundle: string;
  registration_id: string;
  app_version?: string;
  os_version?: string;
}

interface VerifyLinkRequest {
  session_id: string;
  verification_code: string;
}

interface CompleteLinkRequest {
  session_id: string;
}

interface RevokeDeviceRequest {
  device_id: string;
}

export const generateQRForAuth = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const { session_type, device_fingerprint } = request.body as GenerateQRRequest;

    if (!session_type || !device_fingerprint) {
      throw new ValidationError('Session type and device fingerprint are required');
    }

    if (!['login', 'device_link'].includes(session_type)) {
      throw new ValidationError('Invalid session type');
    }

    const clientIp = request.ip;
    const userAgent = request.headers['user-agent'] || '';
    const initiatorDeviceId = request.headers['x-device-id'] as string;

    const result = await qrAuthService.generateQRCode({
      sessionType: session_type,
      initiatorDeviceId,
      initiatorIp: clientIp,
      initiatorUserAgent: userAgent,
      initiatorFingerprint: device_fingerprint
    });

    const response: ApiResponse = {
      success: true,
      message: 'QR code generated successfully',
      data: {
        qr_code: result.qrCode,
        session_id: result.sessionId,
        expires_at: result.expiresAt,
        session_type
      }
    };

    reply.status(201).send(response);
  } catch (error) {
    logger.error('Error generating QR code:', error);
    throw error;
  }
};

export const scanQRForAuth = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const authRequest = request as AuthenticatedRequest;
    const { qr_data } = request.body as ScanQRRequest;

    if (!qr_data) {
      throw new ValidationError('QR data is required');
    }

    if (!authRequest.user?.phone) {
      throw new AuthenticationError('User authentication required');
    }

    const clientIp = request.ip;
    const userAgent = request.headers['user-agent'] || '';
    const deviceId = request.headers['x-device-id'] as string;

    const result = await qrAuthService.scanQRCode(
      qr_data,
      authRequest.user.phone,
      deviceId,
      clientIp,
      userAgent
    );

    const response: ApiResponse = {
      success: true,
      message: 'QR code scanned successfully',
      data: {
        session_id: result.sessionId,
        session_type: result.sessionType,
        challenge: result.challenge,
        nonce: result.nonce,
        expires_at: result.expiresAt
      }
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error scanning QR code:', error);
    throw error;
  }
};

export const approveQRAuth = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const authRequest = request as AuthenticatedRequest;
    const approvalData = request.body as ApproveQRRequest;

    if (!authRequest.user?.phone) {
      throw new AuthenticationError('User authentication required');
    }

    const requiredFields = ['session_id', 'device_name', 'device_type', 'identity_key', 'signed_pre_key', 'pre_key_bundle', 'registration_id', 'device_fingerprint'];
    for (const field of requiredFields) {
      if (!approvalData[field as keyof ApproveQRRequest]) {
        throw new ValidationError(`${field} is required`);
      }
    }

    const result = await qrAuthService.approveQRSession(
      approvalData.session_id,
      {
        deviceName: approvalData.device_name,
        deviceType: approvalData.device_type,
        identityKey: approvalData.identity_key,
        signedPreKey: approvalData.signed_pre_key,
        preKeyBundle: approvalData.pre_key_bundle,
        registrationId: approvalData.registration_id,
        deviceFingerprint: approvalData.device_fingerprint
      },
      authRequest.user.phone
    );

    const response: ApiResponse = {
      success: true,
      message: 'QR authentication approved successfully',
      data: {
        auth_token: result.authToken,
        session_approved: result.success
      }
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error approving QR authentication:', error);
    throw error;
  }
};

export const rejectQRAuth = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const authRequest = request as AuthenticatedRequest;
    const { session_id } = request.body as RejectQRRequest;

    if (!session_id) {
      throw new ValidationError('Session ID is required');
    }

    if (!authRequest.user?.phone) {
      throw new AuthenticationError('User authentication required');
    }

    await qrAuthService.rejectQRSession(session_id, authRequest.user.phone);

    const response: ApiResponse = {
      success: true,
      message: 'QR authentication rejected successfully'
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error rejecting QR authentication:', error);
    throw error;
  }
};

export const checkQRStatus = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const { sessionId } = request.params as { sessionId: string };

    if (!sessionId) {
      throw new ValidationError('Session ID is required');
    }

    const result = await qrAuthService.getSessionStatus(sessionId);

    const response: ApiResponse = {
      success: true,
      message: 'QR session status retrieved successfully',
      data: {
        status: result.status,
        expires_at: result.expiresAt,
        session_type: result.sessionType
      }
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error checking QR status:', error);
    throw error;
  }
};

export const initiateDeviceLink = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const authRequest = request as AuthenticatedRequest;
    const linkData = request.body as InitiateLinkRequest;

    if (!authRequest.user?.phone) {
      throw new AuthenticationError('User authentication required');
    }

    const requiredFields = ['device_name', 'device_type', 'device_fingerprint', 'identity_key', 'signed_pre_key', 'pre_key_bundle', 'registration_id'];
    for (const field of requiredFields) {
      if (!linkData[field as keyof InitiateLinkRequest]) {
        throw new ValidationError(`${field} is required`);
      }
    }

    const clientIp = request.ip;
    const userAgent = request.headers['user-agent'] || '';
    const primaryDeviceId = request.headers['x-device-id'] as string || 'primary';

    const result = await deviceLinkService.initiateLinking({
      userPhone: authRequest.user.phone,
      primaryDeviceId,
      deviceData: {
        deviceName: linkData.device_name,
        deviceType: linkData.device_type,
        deviceFingerprint: linkData.device_fingerprint,
        ipAddress: clientIp,
        userAgent,
        appVersion: linkData.app_version,
        osVersion: linkData.os_version
      },
      cryptoData: {
        identityKey: linkData.identity_key,
        signedPreKey: linkData.signed_pre_key,
        preKeyBundle: linkData.pre_key_bundle,
        registrationId: linkData.registration_id
      }
    });

    const response: ApiResponse = {
      success: true,
      message: 'Device linking initiated successfully',
      data: {
        session_id: result.sessionId,
        verification_code: result.verificationCode,
        expires_at: result.expiresAt
      }
    };

    reply.status(201).send(response);
  } catch (error) {
    logger.error('Error initiating device link:', error);
    throw error;
  }
};

export const verifyDeviceLink = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const authRequest = request as AuthenticatedRequest;
    const { session_id, verification_code } = request.body as VerifyLinkRequest;

    if (!session_id || !verification_code) {
      throw new ValidationError('Session ID and verification code are required');
    }

    if (!authRequest.user?.phone) {
      throw new AuthenticationError('User authentication required');
    }

    const result = await deviceLinkService.verifyLinking(session_id, verification_code, authRequest.user.phone);

    const response: ApiResponse = {
      success: true,
      message: 'Device linking verified successfully',
      data: {
        verified: result.success,
        shared_secret: result.sharedSecret
      }
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error verifying device link:', error);
    throw error;
  }
};

export const completeDeviceLink = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const authRequest = request as AuthenticatedRequest;
    const { session_id } = request.body as CompleteLinkRequest;

    if (!session_id) {
      throw new ValidationError('Session ID is required');
    }

    if (!authRequest.user?.phone) {
      throw new AuthenticationError('User authentication required');
    }

    const result = await deviceLinkService.completeLinking(session_id, authRequest.user.phone);

    const response: ApiResponse = {
      success: true,
      message: 'Device linking completed successfully',
      data: {
        device_id: result.deviceId,
        linked: result.success
      }
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error completing device link:', error);
    throw error;
  }
};

export const revokeLinkedDevice = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const authRequest = request as AuthenticatedRequest;
    const { device_id } = request.body as RevokeDeviceRequest;

    if (!device_id) {
      throw new ValidationError('Device ID is required');
    }

    if (!authRequest.user?.phone) {
      throw new AuthenticationError('User authentication required');
    }

    const revokerDeviceId = request.headers['x-device-id'] as string || 'unknown';

    const result = await deviceLinkService.revokeDevice(authRequest.user.phone, device_id, revokerDeviceId);

    const response: ApiResponse = {
      success: true,
      message: 'Device revoked successfully',
      data: {
        revoked: result.success
      }
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error revoking device:', error);
    throw error;
  }
};

export const listLinkedDevices = async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  try {
    const authRequest = request as AuthenticatedRequest;

    if (!authRequest.user?.phone) {
      throw new AuthenticationError('User authentication required');
    }

    const devices = await deviceLinkService.listDevices(authRequest.user.phone);

    const response: ApiResponse = {
      success: true,
      message: 'Linked devices retrieved successfully',
      data: {
        devices: devices.map(device => ({
          device_id: device.device_id,
          device_name: device.device_name,
          device_type: device.device_type,
          is_primary: device.is_primary,
          is_active: device.is_active,
          last_seen: device.last_seen,
          linked_at: device.linked_at,
          verified_at: device.verified_at
        })),
        total_devices: devices.length
      }
    };

    reply.status(200).send(response);
  } catch (error) {
    logger.error('Error listing devices:', error);
    throw error;
  }
};
