import QRCode from 'qrcode';
import sodium from 'sodium-native';
import { QRSession } from '../models/qr-session.model';
import { AuditLog } from '../models/device-link.model';
import { logger } from '../utils/logger';
import { ValidationError, NotFoundError } from '../utils/errors';

interface QRGenerationOptions {
  sessionType: 'login' | 'device_link';
  initiatorDeviceId?: string;
  initiatorIp: string;
  initiatorUserAgent: string;
  initiatorFingerprint: string;
  expiryMinutes?: number;
}

interface QRScanResult {
  sessionId: string;
  sessionType: 'login' | 'device_link';
  challenge: string;
  nonce: string;
  isValid: boolean;
  expiresAt: Date;
}

interface DeviceApprovalData {
  deviceName: string;
  deviceType: 'ios' | 'android' | 'web' | 'desktop';
  identityKey: string;
  signedPreKey: string;
  preKeyBundle: string;
  registrationId: string;
  deviceFingerprint: string;
}

const QR_EXPIRY_MINUTES = 10;
const MAX_QR_SIZE = 512;
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5;

export const generateQRCode = async (options: QRGenerationOptions): Promise<{ qrCode: string; sessionId: string; expiresAt: Date }> => {
  await checkRateLimit(options.initiatorIp);

  const sessionId = generateSecureSessionId();
  const nonce = generateNonce();
  const challenge = generateChallenge();
  const salt = generateSalt();

  const expiryMinutes = options.expiryMinutes || QR_EXPIRY_MINUTES;
  const expiresAt = new Date(Date.now() + expiryMinutes * 60 * 1000);

  const qrData = {
    v: '1.0',
    t: options.sessionType,
    s: sessionId,
    c: challenge,
    n: nonce,
    e: Math.floor(expiresAt.getTime() / 1000)
  };

  const qrCodeData = JSON.stringify(qrData);

  if (qrCodeData.length > MAX_QR_SIZE) {
    throw new ValidationError('QR code data too large');
  }

  const session = new QRSession({
    session_id: sessionId,
    qr_code_data: qrCodeData,
    session_type: options.sessionType,
    status: 'pending',
    initiator_device_id: options.initiatorDeviceId,
    initiator_ip: options.initiatorIp,
    initiator_user_agent: options.initiatorUserAgent,
    initiator_fingerprint: options.initiatorFingerprint,
    security_data: {
      nonce,
      challenge,
      salt
    },
    expires_at: expiresAt
  });

  await session.save();

  await logAuditEvent('qr_generated', {
    session_id: sessionId,
    session_type: options.sessionType,
    ip_address: options.initiatorIp,
    user_agent: options.initiatorUserAgent,
    event_data: {
      device_id: options.initiatorDeviceId,
      fingerprint: options.initiatorFingerprint,
      expires_at: expiresAt
    }
  });

  const qrCodeImage = await QRCode.toDataURL(qrCodeData, {
    errorCorrectionLevel: 'M',
    type: 'image/png',
    margin: 1,
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    }
  });

  logger.info(`QR code generated for session ${sessionId}, type: ${options.sessionType}`);

  return {
    qrCode: qrCodeImage,
    sessionId,
    expiresAt
  };
};

export const scanQRCode = async (qrData: string, scannerPhone: string, scannerDeviceId: string, scannerIp: string, scannerUserAgent: string): Promise<QRScanResult> => {
  let parsedData;
  try {
    parsedData = JSON.parse(qrData);
  } catch (error) {
    throw new ValidationError('Invalid QR code format');
  }

  if (!parsedData.s || !parsedData.c || !parsedData.n || !parsedData.t) {
    throw new ValidationError('Missing required QR code fields');
  }

  const session = await QRSession.findOne({
    session_id: parsedData.s,
    status: 'pending'
  });

  if (!session) {
    throw new NotFoundError('QR session not found or already processed');
  }

  if (session.expires_at < new Date()) {
    session.status = 'expired';
    await session.save();
    throw new ValidationError('QR code has expired');
  }

  session.status = 'scanned';
  session.scanner_phone = scannerPhone;
  session.scanner_device_id = scannerDeviceId;
  session.scanner_ip = scannerIp;
  session.scanner_user_agent = scannerUserAgent;
  session.scanned_at = new Date();
  await session.save();

  await logAuditEvent('qr_scanned', {
    session_id: session.session_id,
    user_phone: scannerPhone,
    device_id: scannerDeviceId,
    ip_address: scannerIp,
    user_agent: scannerUserAgent,
    event_data: {
      session_type: session.session_type,
      initiator_ip: session.initiator_ip
    }
  });

  logger.info(`QR code scanned by ${scannerPhone} for session ${session.session_id}`);

  return {
    sessionId: session.session_id,
    sessionType: session.session_type,
    challenge: session.security_data.challenge,
    nonce: session.security_data.nonce,
    isValid: true,
    expiresAt: session.expires_at
  };
};

export const approveQRSession = async (sessionId: string, approvalData: DeviceApprovalData, scannerPhone: string): Promise<{ success: boolean; authToken?: string }> => {
    const session = await QRSession.findOne({ 
      session_id: sessionId,
      status: 'scanned',
      scanner_phone: scannerPhone
    });

    if (!session) {
      throw new NotFoundError('QR session not found or not in correct state');
    }

    if (session.expires_at < new Date()) {
      session.status = 'expired';
      await session.save();
      throw new ValidationError('QR session has expired');
    }

    session.status = 'approved';
    session.approval_data = {
      device_name: approvalData.deviceName,
      device_type: approvalData.deviceType,
      identity_key: approvalData.identityKey,
      signed_pre_key: approvalData.signedPreKey,
      pre_key_bundle: approvalData.preKeyBundle,
      registration_id: approvalData.registrationId,
      device_fingerprint: approvalData.deviceFingerprint
    };
    session.approved_at = new Date();
    await session.save();

    await logAuditEvent('device_linked', {
      session_id: sessionId,
      user_phone: scannerPhone,
      device_id: approvalData.deviceFingerprint,
      ip_address: session.scanner_ip || '',
      user_agent: session.scanner_user_agent || '',
      event_data: {
        device_name: approvalData.deviceName,
        device_type: approvalData.deviceType,
        session_type: session.session_type
      }
    });

    if (session.session_type === 'login') {
      const { Auth } = await import('../middleware/auth.middleware');
      const authToken = Auth.generateAccessToken(scannerPhone);
      
      session.status = 'consumed';
      session.consumed_at = new Date();
      await session.save();

      logger.info(`Login approved for session ${sessionId}, user: ${scannerPhone}`);
    return { success: true, authToken };
  }

  logger.info(`Device linking approved for session ${sessionId}, user: ${scannerPhone}`);
  return { success: true };
};

export const rejectQRSession = async (sessionId: string, scannerPhone: string): Promise<void> => {
    const session = await QRSession.findOne({ 
      session_id: sessionId,
      status: 'scanned',
      scanner_phone: scannerPhone
    });

    if (!session) {
      throw new NotFoundError('QR session not found or not in correct state');
    }

    session.status = 'rejected';
    await session.save();

  logger.info(`QR session ${sessionId} rejected by ${scannerPhone}`);
};

export const getSessionStatus = async (sessionId: string): Promise<{ status: string; expiresAt: Date; sessionType: string }> => {
    const session = await QRSession.findOne({ session_id: sessionId });

    if (!session) {
      throw new NotFoundError('QR session not found');
    }

    if (session.expires_at < new Date() && session.status === 'pending') {
      session.status = 'expired';
      await session.save();
    }

  return {
    status: session.status,
    expiresAt: session.expires_at,
    sessionType: session.session_type
  };
};

const generateSecureSessionId = (): string => {
    const buffer = Buffer.allocUnsafe(32);
    sodium.randombytes_buf(buffer);
  return buffer.toString('hex');
};

const generateNonce = (): string => {
  const buffer = Buffer.allocUnsafe(24);
  sodium.randombytes_buf(buffer);
  return buffer.toString('base64');
};

const generateChallenge = (): string => {
  const buffer = Buffer.allocUnsafe(32);
  sodium.randombytes_buf(buffer);
  return buffer.toString('base64');
};

const generateSalt = (): string => {
  const buffer = Buffer.allocUnsafe(16);
  sodium.randombytes_buf(buffer);
  return buffer.toString('base64');
};

const checkRateLimit = async (ip: string): Promise<void> => {
  const windowStart = new Date(Date.now() - RATE_LIMIT_WINDOW);
  const recentRequests = await QRSession.countDocuments({
    initiator_ip: ip,
    created_at: { $gte: windowStart }
  });

  if (recentRequests >= RATE_LIMIT_MAX_REQUESTS) {
    throw new ValidationError('Rate limit exceeded. Please try again later.');
  }
};

const logAuditEvent = async (eventType: string, data: any): Promise<void> => {
  try {
    const auditLog = new AuditLog({
      event_type: eventType,
      user_phone: data.user_phone,
      device_id: data.device_id,
      session_id: data.session_id,
      ip_address: data.ip_address,
      user_agent: data.user_agent,
      event_data: data.event_data || {}
    });
    await auditLog.save();
  } catch (error) {
    logger.error('Failed to log audit event:', error);
  }
};
