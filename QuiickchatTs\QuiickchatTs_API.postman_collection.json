{"info": {"name": "QuiickchatTs API", "description": "Complete API documentation for QuiickchatTs backend with JWT authentication, QR authentication, status management, contact sync, media upload for chat, and Zengo integration", "version": "2.2.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "phone_number", "value": "+**********", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "session_id", "value": "", "type": "string"}, {"key": "status_id", "value": "", "type": "string"}, {"key": "device_fingerprint", "value": "web-browser-fingerprint-123", "type": "string"}, {"key": "contact_id", "value": "", "type": "string"}, {"key": "verification_code", "value": "123456", "type": "string"}, {"key": "public_id", "value": "chat-media/+**********/sample_file_id", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Initialize User", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/auth/init", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "init"]}, "description": "Initialize user session and check server status. This is typically the first endpoint to call."}}, {"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}, "description": "Register a new user with phone number. Sends SMS verification code."}}, {"name": "Verify Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\",\n  \"code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.auth_tokens) {", "        pm.collectionVariables.set('access_token', response.data.auth_tokens.access_token);", "        pm.collectionVariables.set('refresh_token', response.data.auth_tokens.refresh_token);", "    }", "    if (response.data && response.data.user) {", "        pm.collectionVariables.set('user_id', response.data.user.id);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.auth_tokens) {", "        pm.collectionVariables.set('access_token', response.data.auth_tokens.access_token);", "        pm.collectionVariables.set('refresh_token', response.data.auth_tokens.refresh_token);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\",\n  \"code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/verify-login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "verify-login"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.auth_tokens) {", "        pm.collectionVariables.set('access_token', response.data.auth_tokens.access_token);", "        pm.collectionVariables.set('refresh_token', response.data.auth_tokens.refresh_token);", "    }", "    if (response.data && response.data.user) {", "        pm.collectionVariables.set('user_id', response.data.user.id);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh-token", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh-token"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.auth_tokens) {", "        pm.collectionVariables.set('access_token', response.data.auth_tokens.access_token);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Resend Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/resend-code", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "resend-code"]}}}]}, {"name": "Users", "item": [{"name": "Initialize User", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/users/init", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "init"]}}}, {"name": "Get Current User", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/users/me?phone={{phone_number}}&user_id={{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "me"], "query": [{"key": "phone", "value": "{{phone_number}}", "description": "User's phone number"}, {"key": "user_id", "value": "{{user_id}}", "description": "User's ID"}]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"Updated Username\",\n  \"bio\": \"Updated bio\",\n  \"address\": \"Updated address\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/update-profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "update-profile"]}}}, {"name": "Upload Profile Picture", "request": {"method": "POST", "body": {"mode": "formdata", "formdata": [{"key": "profile_picture", "type": "file", "src": [], "description": "Profile picture image file (JPEG, PNG, etc.)"}]}, "url": {"raw": "{{base_url}}/api/v1/users/upload-profile-picture", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "upload-profile-picture"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.profile_picture) {", "        console.log('Profile picture uploaded:', response.data.profile_picture);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Delete Account", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/api/v1/users/delete-account", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "delete-account"]}}}]}, {"name": "Contacts", "item": [{"name": "Upload Contacts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contacts\": [\n    {\n      \"display_name\": \"Contact 1\",\n      \"phone_number\": \"+**********\"\n    },\n    {\n      \"display_name\": \"Contact 2\",\n      \"phone_number\": \"+**********\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/contacts/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "upload"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data) {", "        console.log('Contacts processed:', response.data.contacts_processed);", "        console.log('Registered contacts:', response.data.contacts_registered);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Get Contacts", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/contacts", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.contacts && response.data.contacts.length > 0) {", "        pm.collectionVariables.set('contact_id', response.data.contacts[0].contact_id);", "        console.log('Total contacts:', response.data.total_count);", "        console.log('Registered contacts:', response.data.registered_count);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Sync Contacts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contacts\": [\n    {\n      \"display_name\": \"Synced Contact\",\n      \"phone_number\": \"+1234567893\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/contacts/sync", "host": ["{{base_url}}"], "path": ["api", "v1", "contacts", "sync"]}}}]}, {"name": "Status", "item": [{"name": "Create Status (Text)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"content_type\": \"text\",\n  \"content\": \"My new status update!\",\n  \"background_color\": \"#FF5733\",\n  \"text_color\": \"#FFFFFF\",\n  \"font_style\": \"bold\",\n  \"privacy_setting\": \"Contacts\",\n  \"allowed_contacts\": [],\n  \"blocked_contacts\": []\n}"}, "url": {"raw": "{{base_url}}/api/v1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "status"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.collectionVariables.set('status_id', response.data.id);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Upload Status Media", "request": {"method": "POST", "body": {"mode": "formdata", "formdata": [{"key": "media", "type": "file", "src": [], "description": "Image or video file (JPEG, PNG, MP4, etc.)"}, {"key": "caption", "value": "Status with media", "type": "text", "description": "Optional caption for the media"}, {"key": "background_color", "value": "#000000", "type": "text", "description": "Optional background color"}, {"key": "text_color", "value": "#FFFFFF", "type": "text", "description": "Optional text color"}, {"key": "font_style", "value": "normal", "type": "text", "description": "Optional font style"}, {"key": "privacy_setting", "value": "Contacts", "type": "text", "description": "Privacy setting: Everyone, Contacts, ContactsExcept, OnlyShare"}, {"key": "allowed_contacts", "value": "[]", "type": "text", "description": "JSON array of allowed contact IDs (for OnlyShare)"}, {"key": "blocked_contacts", "value": "[]", "type": "text", "description": "JSON array of blocked contact IDs (for ContactsExcept)"}]}, "url": {"raw": "{{base_url}}/api/v1/status/upload", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "upload"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.collectionVariables.set('status_id', response.data.id);", "        console.log('Status created with media:', response.data.content_url);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Get My Statuses", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/status/my", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "my"]}}}, {"name": "Get Contact Statuses (Feed)", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/status/feed", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "feed"]}}}, {"name": "Get User Statuses", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/status/user/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "user", "{{user_id}}"]}}}, {"name": "View Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status_id\": \"{{status_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/status/view", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "view"]}}}, {"name": "Get Status Views", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/status/{{status_id}}/views", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "{{status_id}}", "views"]}}}, {"name": "Delete Status", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/api/v1/status/{{status_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "{{status_id}}"]}}}, {"name": "Get Privacy Settings", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/status/privacy", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "privacy"]}}}, {"name": "Update Privacy Settings", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"default_privacy_setting\": \"Contacts\",\n  \"blocked_contacts\": [],\n  \"allowed_contacts\": []\n}"}, "url": {"raw": "{{base_url}}/api/v1/status/privacy", "host": ["{{base_url}}"], "path": ["api", "v1", "status", "privacy"]}}}]}, {"name": "QR Authentication", "item": [{"name": "Generate QR Code", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Device-Fingerprint", "value": "{{device_fingerprint}}", "description": "Device fingerprint for security"}], "body": {"mode": "raw", "raw": "{\n  \"session_type\": \"login\",\n  \"device_fingerprint\": \"{{device_fingerprint}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/qr/generate", "host": ["{{base_url}}"], "path": ["api", "v1", "qr", "generate"]}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.session_id) {", "        pm.collectionVariables.set('session_id', response.data.session_id);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Check QR Status", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/qr/status/{{session_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "qr", "status", "{{session_id}}"]}}}, {"name": "Scan QR Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Device-ID", "value": "mobile-device-123", "description": "Mobile device identifier"}], "body": {"mode": "raw", "raw": "{\n  \"qr_data\": \"{\\\"v\\\":\\\"1.0\\\",\\\"t\\\":\\\"login\\\",\\\"s\\\":\\\"{{session_id}}\\\",\\\"c\\\":\\\"challenge\\\",\\\"n\\\":\\\"nonce\\\",\\\"e\\\":1704110520}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/qr/scan", "host": ["{{base_url}}"], "path": ["api", "v1", "qr", "scan"]}}}, {"name": "Approve QR Auth", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\",\n  \"device_name\": \"My Desktop\",\n  \"device_type\": \"web\",\n  \"identity_key\": \"identity_key_here\",\n  \"signed_pre_key\": \"signed_pre_key_here\",\n  \"pre_key_bundle\": \"pre_key_bundle_here\",\n  \"registration_id\": \"registration_id_here\",\n  \"device_fingerprint\": \"{{device_fingerprint}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/qr/approve", "host": ["{{base_url}}"], "path": ["api", "v1", "qr", "approve"]}}}, {"name": "Reject QR Auth", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/qr/reject", "host": ["{{base_url}}"], "path": ["api", "v1", "qr", "reject"]}}}, {"name": "Initiate Device Link", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"device_name\": \"My Tablet\",\n  \"device_type\": \"android\",\n  \"device_fingerprint\": \"{{device_fingerprint}}\",\n  \"identity_key\": \"identity_key_here\",\n  \"signed_pre_key\": \"signed_pre_key_here\",\n  \"pre_key_bundle\": \"pre_key_bundle_here\",\n  \"registration_id\": \"registration_id_here\",\n  \"app_version\": \"1.0.0\",\n  \"os_version\": \"Android 12\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/qr/device/initiate", "host": ["{{base_url}}"], "path": ["api", "v1", "qr", "device", "initiate"]}}}, {"name": "Verify Devi<PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\",\n  \"verification_code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/qr/device/verify", "host": ["{{base_url}}"], "path": ["api", "v1", "qr", "device", "verify"]}}}, {"name": "Complete Device Link", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"session_id\": \"{{session_id}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/qr/device/complete", "host": ["{{base_url}}"], "path": ["api", "v1", "qr", "device", "complete"]}}}, {"name": "Revoke Linked Device", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"device_id\": \"device_id_here\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/qr/device/revoke", "host": ["{{base_url}}"], "path": ["api", "v1", "qr", "device", "revoke"]}}}, {"name": "List Linked Devices", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/qr/devices/list", "host": ["{{base_url}}"], "path": ["api", "v1", "qr", "devices", "list"]}}}]}, {"name": "Deep Links", "item": [{"name": "Generate Deep Link", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"qr_scan\",\n  \"session_id\": \"{{session_id}}\",\n  \"metadata\": {\n    \"platform\": \"mobile\",\n    \"version\": \"1.0.0\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/v1/link/generate", "host": ["{{base_url}}"], "path": ["api", "v1", "link", "generate"]}}}, {"name": "Parse Deep Link", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"url\": \"quickchat://auth/qr-scan?data=...\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/link/parse", "host": ["{{base_url}}"], "path": ["api", "v1", "link", "parse"]}}}, {"name": "QR Scan Redirect", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/link/qr-scan/{{session_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "link", "qr-scan", "{{session_id}}"]}}}, {"name": "Device Link Redirect", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/link/device-link/{{session_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "link", "device-link", "{{session_id}}"]}}}, {"name": "Get App Store Links", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/link/app-store", "host": ["{{base_url}}"], "path": ["api", "v1", "link", "app-store"]}}}, {"name": "Generate Smart Banner", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/link/smart-banner/{{session_id}}/qr_scan", "host": ["{{base_url}}"], "path": ["api", "v1", "link", "smart-banner", "{{session_id}}", "qr_scan"]}}}, {"name": "Validate Deep Link Session", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/link/validate/{{session_id}}/qr_scan", "host": ["{{base_url}}"], "path": ["api", "v1", "link", "validate", "{{session_id}}", "qr_scan"]}}}]}, {"name": "System", "item": [{"name": "SMS Service Status", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/system/sms-status", "host": ["{{base_url}}"], "path": ["api", "v1", "system", "sms-status"]}}}, {"name": "Test SMS Service", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/system/test-sms", "host": ["{{base_url}}"], "path": ["api", "v1", "system", "test-sms"]}}}, {"name": "System Health Check", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/system/health", "host": ["{{base_url}}"], "path": ["api", "v1", "system", "health"]}}}]}, {"name": "<PERSON> (Zengo)", "item": [{"name": "Get Service Status", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/api/v1/z/status", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "status"]}}}, {"name": "Register Current User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"UserInfo\": [\n    {\n      \"UserId\": \"{{user_id}}\",\n      \"UserName\": \"<PERSON>\",\n      \"UserAvatar\": \"https://example.com/avatar.jpg\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/register", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "register"]}}}, {"name": "Get User Online Status", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/z/users/online-status?UserIds={{user_id}},user456", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "online-status"], "query": [{"key": "UserIds", "value": "{{user_id}},user456", "description": "Comma-separated list of user IDs"}]}}}, {"name": "Get User Info", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/z/users/info?UserIds={{user_id}},user456", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "info"], "query": [{"key": "UserIds", "value": "{{user_id}},user456", "description": "Comma-separated list of user IDs"}]}}}, {"name": "Modify User Info", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"UserInfo\": [\n    {\n      \"UserId\": \"{{user_id}}\",\n      \"UserName\": \"Updated Name\",\n      \"UserAvatar\": \"https://example.com/new-avatar.jpg\",\n      \"Extra\": \"additional_info\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/users/modify", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "modify"]}}}, {"name": "Add Friends", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FromUserId\": \"{{user_id}}\",\n  \"FriendInfos\": [\n    {\n      \"UserId\": \"user456\",\n      \"Wording\": \"search\",\n      \"FriendAlias\": \"Best friend\",\n      \"FriendTime\": 1640995200,\n      \"Attributes\": [\n        {\n          \"Key\": \"k0\",\n          \"Value\": \"custom_value\"\n        }\n      ]\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/add", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "add"]}}}, {"name": "Send Friend Requests", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FromUserId\": \"{{user_id}}\",\n  \"FriendInfos\": [\n    {\n      \"UserId\": \"user456\",\n      \"Wording\": \"Hi, let's be friends!\",\n      \"FriendAlias\": \"New Friend\",\n      \"CreateTime\": 1640995200,\n      \"UpdateTime\": 1640995200,\n      \"Attributes\": [\n        {\n          \"Key\": \"k0\",\n          \"Value\": \"friend_request\"\n        }\n      ]\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/request", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "request"]}}}, {"name": "Delete Friends", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FromUserId\": \"{{user_id}}\",\n  \"UserIds\": [\"user456\"],\n  \"DeleteType\": 0\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/delete", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "delete"]}}}, {"name": "Delete All Friends", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FromUserId\": \"{{user_id}}\",\n  \"DeleteType\": 0\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/delete-all", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "delete-all"]}}}, {"name": "Query Friend List", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/z/friends/list?FromUserId={{user_id}}&StartIndex=0&StandardSequence=1&CustomSequence=0", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "list"], "query": [{"key": "FromUserId", "value": "{{user_id}}", "description": "User ID to get friends for"}, {"key": "StartIndex", "value": "0", "description": "Starting index for pagination"}, {"key": "StandardSequence", "value": "1", "description": "Standard sequence number"}, {"key": "CustomSequence", "value": "0", "description": "Custom sequence number"}]}}}, {"name": "Update Friends Alias", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FromUserId\": \"{{user_id}}\",\n  \"UserIds\": [\n    {\n      \"UserId\": \"user456\",\n      \"FriendAlias\": \"New nickname\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/update-alias", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "update-alias"]}}}, {"name": "Update Friend Attributes", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FromUserId\": \"{{user_id}}\",\n  \"UserId\": \"user456\",\n  \"Attributes\": [\n    {\n      \"Key\": \"k0\",\n      \"Value\": \"custom_attribute_value\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/friends/update-attributes", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "friends", "update-attributes"]}}}, {"name": "Block Users", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FromUserId\": \"{{user_id}}\",\n  \"UserIds\": [\"user456\", \"user789\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/users/block", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "block"]}}}, {"name": "Unblock Users", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FromUserId\": \"{{user_id}}\",\n  \"UserIds\": [\"user456\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/users/unblock", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "unblock"]}}}, {"name": "Query Blocklist", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/v1/z/users/blocklist?FromUserId={{user_id}}&StartIndex=0", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "blocklist"], "query": [{"key": "FromUserId", "value": "{{user_id}}", "description": "User ID to get blocklist for"}, {"key": "StartIndex", "value": "0", "description": "Starting index for pagination"}]}}}, {"name": "Check Blockship", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FromUserId\": \"{{user_id}}\",\n  \"UserIds\": [\"user456\", \"user789\"]\n}"}, "url": {"raw": "{{base_url}}/api/v1/z/users/check-blockship", "host": ["{{base_url}}"], "path": ["api", "v1", "z", "users", "check-blockship"]}}}]}, {"name": "Media Upload", "item": [{"name": "Upload Single Media File", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Media file to upload (images, videos, audio, documents)"}]}, "url": {"raw": "{{base_url}}/api/v1/upload/chat-media", "host": ["{{base_url}}"], "path": ["api", "v1", "upload", "chat-media"]}, "description": "Upload a single media file for chat. Supports:\n- Images: JPEG, PNG, GIF, WebP (max 10MB)\n- Videos: MP4, MPEG, QuickTime, WebM (max 100MB)\n- Audio: MP3, WAV, OGG (max 50MB)\n- Documents: PDF, TXT (max 25MB)\n\nReturns a URL that can be used with Zengo for chat messages."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/upload/chat-media", "host": ["{{base_url}}"], "path": ["api", "v1", "upload", "chat-media"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Media uploaded successfully\",\n  \"data\": {\n    \"url\": \"https://res.cloudinary.com/dpwisibis/image/upload/v**********/chat-media/+**********/abc123.jpg\",\n    \"public_id\": \"chat-media/+**********/abc123\",\n    \"media_type\": \"image\",\n    \"file_size\": 1048576,\n    \"mime_type\": \"image/jpeg\",\n    \"filename\": \"photo.jpg\",\n    \"uploaded_at\": \"2023-11-20T10:30:00Z\"\n  }\n}"}]}, {"name": "Upload Multiple Media Files", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": [], "description": "Multiple media files (max 10 files)"}, {"key": "files", "type": "file", "src": [], "description": "Additional file slot"}, {"key": "files", "type": "file", "src": [], "description": "Additional file slot"}]}, "url": {"raw": "{{base_url}}/api/v1/upload/chat-media/multiple", "host": ["{{base_url}}"], "path": ["api", "v1", "upload", "chat-media", "multiple"]}, "description": "Upload multiple media files at once (maximum 10 files). Each file follows the same size and type restrictions as single upload."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/upload/chat-media/multiple", "host": ["{{base_url}}"], "path": ["api", "v1", "upload", "chat-media", "multiple"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"5 files uploaded successfully\",\n  \"data\": {\n    \"files\": [\n      {\n        \"url\": \"https://res.cloudinary.com/dpwisibis/image/upload/v**********/chat-media/+**********/abc123.jpg\",\n        \"public_id\": \"chat-media/+**********/abc123\",\n        \"media_type\": \"image\",\n        \"file_size\": 1048576,\n        \"mime_type\": \"image/jpeg\",\n        \"filename\": \"photo1.jpg\",\n        \"uploaded_at\": \"2023-11-20T10:30:00Z\"\n      }\n    ],\n    \"total_uploaded\": 5\n  }\n}"}]}, {"name": "Delete Uploaded Media", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/upload/chat-media/{{public_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "upload", "chat-media", "{{public_id}}"]}, "description": "Delete an uploaded media file using its public_id. The public_id is returned when uploading a file."}, "response": [{"name": "Success Response", "originalRequest": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/v1/upload/chat-media/chat-media/+**********/abc123", "host": ["{{base_url}}"], "path": ["api", "v1", "upload", "chat-media", "chat-media", "+**********", "abc123"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"message\": \"Media deleted successfully\"\n}"}]}]}, {"name": "Health & Utilities", "item": [{"name": "Root Health Check", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Basic health check endpoint at root level"}}]}]}