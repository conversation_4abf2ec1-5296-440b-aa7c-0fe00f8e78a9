import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import jwt from 'jsonwebtoken';
import { AuthenticationError, ValidationError } from '../utils/errors';
import { logger } from '../utils/logger';
import { generateJwtId } from '../utils/helpers';
import { JWTClaims, AuthenticatedRequest } from '../types';

const PUBLIC_PATHS = new Set([
  '/api/v1/auth/register',
  '/api/v1/auth/login',
  '/api/v1/auth/verify',
  '/api/v1/auth/verify-login',
  '/api/v1/auth/refresh-token',
  '/api/v1/auth/resend-code',
  '/api/v1/users/init',
  '/api/v1/z/status',
  '/status-updates',
  '/health',
  '/static',
  '/uploads'
]);


export async function authMiddleware(fastify: FastifyInstance) {
  fastify.addHook('preHandler', async (request: FastifyRequest, reply: FastifyReply) => {
    const path = (request as any).routerPath || request.url.split('?')[0];

    if (Auth.isPublicPath(path)) {
      logger.info(`Public path accessed: ${path}`);
      return;
    }

    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('Authorization header is required');
    }

    const token = authHeader.substring(7);

    try {
      const claims = Auth.validateAccessToken(token);
      (request as AuthenticatedRequest).user = {
        phone: claims.phone,
        roles: claims.roles || ['user']
      };

      logger.info(`Authenticated request for phone: ${claims.phone}`);
    } catch (error) {
      logger.error(`Authentication failed: ${error}`);
      throw error;
    }
  });
}

export async function authenticateRequest(request: FastifyRequest, reply: FastifyReply) {
  const authHeader = request.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new AuthenticationError('Authorization header is required');
  }

  const token = authHeader.substring(7);

  try {
    const claims = Auth.validateAccessToken(token);
    (request as AuthenticatedRequest).user = {
      phone: claims.phone,
      roles: claims.roles || ['user']
    };

    logger.info(`Authenticated request for phone: ${claims.phone}`);
  } catch (error) {
    logger.error(`Authentication failed: ${error}`);
    throw error;
  }
}

export class Auth {
  
  static generateTokens(phone: string, roles: string[] = ['user']): { access_token: string; refresh_token: string } {
    const accessToken = this.generateAccessToken(phone, roles);
    const refreshToken = this.generateRefreshToken(phone);

    return {
      access_token: accessToken,
      refresh_token: refreshToken
    };
  }

  /**
   * Generate access token (short-lived)
   */
  static generateAccessToken(phone: string, roles: string[] = ['user']): string {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error('JWT_SECRET environment variable is required');
    }

    const tokenId = generateJwtId();
    const now = Math.floor(Date.now() / 1000);
    const accessTokenExpiryMinutes = parseInt(process.env.ACCESS_TOKEN_EXPIRY_MINUTES || '60');
    const expiresAt = now + (accessTokenExpiryMinutes * 60);

    // Only include roles if the user has special privil

    const filteredRoles = (roles.length === 1 && roles.includes('user')) ? [] : roles;

    const claims: JWTClaims = {
      phone,
      exp: expiresAt,
      iat: now,
      jti: tokenId,
      token_type: 'access',
      ...(filteredRoles.length > 0 && { roles: filteredRoles })
    };

    return jwt.sign(claims, secret, { algorithm: 'HS256' });
  }

  /**
   * Generate refresh token (long-lived)
   */
  static generateRefreshToken(phone: string): string {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new Error('JWT_SECRET environment variable is required');
    }

    const tokenId = generateJwtId();
    const now = Math.floor(Date.now() / 1000);
    const refreshTokenExpiryDays = parseInt(process.env.REFRESH_TOKEN_EXPIRY_DAYS || '30');
    const expiresAt = now + (refreshTokenExpiryDays * 24 * 60 * 60);

    const claims: JWTClaims = {
      phone,
      exp: expiresAt,
      iat: now,
      jti: tokenId,
      token_type: 'refresh'
    };

    return jwt.sign(claims, secret, { algorithm: 'HS256' });
  }

  /**
   * backward compatibility
   */
  static generateToken(userId: string, phone?: string, roles: string[] = ['user']): string {
    const phoneToUse = phone || userId;
    logger.warn(`Using legacy token generation method for: ${phoneToUse}`);
    return this.generateAccessToken(phoneToUse, roles);
  }

  /**
   * Validate any token 
   */
  static validateToken(token: string): JWTClaims {
    try {
      return this.validateAccessToken(token);
    } catch (accessErr) {
      try {
        return this.validateRefreshToken(token);
      } catch (refreshErr) {
        throw accessErr;
      }
    }
  }

  /**
   * Validate access token
   */
  static validateAccessToken(token: string): JWTClaims {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new AuthenticationError('JWT_SECRET environment variable is required');
    }

    try {
      const decoded = jwt.verify(token, secret, { algorithms: ['HS256'] }) as JWTClaims;
      
      if (decoded.token_type !== 'access') {
        throw new AuthenticationError('Invalid token type for access token');
      }

      logger.info(`Successfully validated access token for phone: ${decoded.phone}`);
      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthenticationError('Access token has expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new AuthenticationError('Invalid access token');
      }
      throw error;
    }
  }

  /**
   * Validate refresh token
   */
  static validateRefreshToken(token: string): JWTClaims {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      throw new AuthenticationError('JWT_SECRET environment variable is required');
    }

    try {
      const decoded = jwt.verify(token, secret, { algorithms: ['HS256'] }) as JWTClaims;
      
      if (decoded.token_type !== 'refresh') {
        throw new AuthenticationError('Invalid token type for refresh token');
      }

      logger.info(`Successfully validated refresh token for phone: ${decoded.phone}`);
      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthenticationError('Refresh token has expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new AuthenticationError('Invalid refresh token');
      }
      throw error;
    }
  }

  /**
   * Check if a user has a specific role
   */
  static hasRole(claims: JWTClaims, role: string): boolean {
    if (role === 'user') {
      return true;
    }

    // Otherwise, check if the role exists in the roles list
    return claims.roles?.includes(role) || false;
  }

  /**
   * Check if a path is public
   */
  static isPublicPath(path: string): boolean {
    if (!path) return false;

    const cleanPath = path.split('?')[0];
    if (!cleanPath) return false;

    if (PUBLIC_PATHS.has(cleanPath)) {
      return true;
    }

    const prefixPaths = ['/health', '/static', '/uploads', '/status-updates'];
    for (const prefixPath of prefixPaths) {
      if (cleanPath.startsWith(prefixPath)) {
        return true;
      }
    }

    return false;
  }
}
