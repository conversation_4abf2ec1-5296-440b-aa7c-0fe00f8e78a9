import dotenv from 'dotenv';
import { DatabaseConfig, TwilioConfig, CloudinaryConfig, ZengoConfig } from '../types';

dotenv.config();

export const config = {
  server: {
    port: parseInt(process.env.PORT || '3000'),
    host: process.env.HOST || '0.0.0.0',
    nodeEnv: process.env.NODE_ENV || 'development'
  },

  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/quickchat',
    dbName: process.env.MONGODB_DATABASE || 'quickchat'
  },

  jwt: {
    secret: process.env.JWT_SECRET || 'default-secret-change-in-production',
    accessTokenExpiryMinutes: parseInt(process.env.ACCESS_TOKEN_EXPIRY_MINUTES || '60'),
    refreshTokenExpiryDays: parseInt(process.env.REFRESH_TOKEN_EXPIRY_DAYS || '30')
  },

  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID || '',
    authToken: process.env.TWILIO_AUTH_TOKEN || '',
    fromNumber: process.env.TWILIO_FROM_NUMBER || '',
    enabled: process.env.TWILIO_ENABLED === 'true'
  },

  cloudinary: {
    cloudName: process.env.CLOUDINARY_CLOUD_NAME || '',
    apiKey: process.env.CLOUDINARY_API_KEY || '',
    apiSecret: process.env.CLOUDINARY_API_SECRET || ''
  },

  zengo: {
    appId: process.env.ZENGO_APP_ID || '',
    serverSecret: process.env.ZENGO_SERVER_SECRET || '',
    region: (process.env.ZENGO_REGION as ZengoConfig['region']) || 'unified',
    enabled: process.env.ZENGO_ENABLED === 'true'
  },

  development: {
    includeCodeInResponse: process.env.INCLUDE_CODE_IN_RESPONSE === 'true',
    logLevel: process.env.LOG_LEVEL || 'info'
  },

  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    credentials: process.env.CORS_CREDENTIALS === 'true'
  }
};

export default config;
