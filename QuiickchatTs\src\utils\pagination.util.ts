import { PaginationQuery, PaginationMeta } from '../types';

interface PaginationLimits {
  maxLimit: number;
  defaultLimit: number;
  maxPage: number;
  maxCursorLength: number;
}

export class PaginationUtil {
  private static readonly LIMITS: PaginationLimits = {
    maxLimit: 100,
    defaultLimit: 50,
    maxPage: 10000,
    maxCursorLength: 200
  };

  private static readonly ENDPOINT_LIMITS: Record<string, Partial<PaginationLimits>> = {
    'contacts': { maxLimit: 500, defaultLimit: 100 },
    'contacts-all': { maxLimit: 1000, defaultLimit: 200 },
    'status': { maxLimit: 50, defaultLimit: 20 },
    'admin': { maxLimit: 1000, defaultLimit: 100 }
  };
  static parsePaginationQuery(query: any, endpoint?: string): Required<Pick<PaginationQuery, 'page' | 'limit'>> & Pick<PaginationQuery, 'next'> {
    const limits = this.getLimitsForEndpoint(endpoint);

    const requestedPage = parseInt(query?.page);
    const requestedLimit = parseInt(query?.limit);

    const page = (requestedPage && requestedPage > 0 && requestedPage <= limits.maxPage) ? requestedPage : 1;
    const limit = (requestedLimit && requestedLimit > 0 && requestedLimit <= limits.maxLimit) ? requestedLimit : limits.defaultLimit;
    const next = (typeof query?.next === 'string' && query.next.length <= limits.maxCursorLength) ? query.next : undefined;

    return { page, limit, next };
  }

  private static getLimitsForEndpoint(endpoint?: string): PaginationLimits {
    if (!endpoint) return this.LIMITS;

    const endpointOverrides = this.ENDPOINT_LIMITS[endpoint];
    if (!endpointOverrides) return this.LIMITS;

    return {
      ...this.LIMITS,
      ...endpointOverrides
    };
  }

  static calculatePaginationMeta(
    page: number,
    limit: number,
    total: number,
    next?: string,
    prev?: string
  ): PaginationMeta {
    const pages = Math.ceil(total / limit);
    const hasNext = page < pages;
    const hasPrev = page > 1;

    const meta: PaginationMeta = {
      page,
      limit,
      total,
      pages,
      hasNext,
      hasPrev
    };

    if (hasNext && next) {
      meta.next = next;
    }
    if (hasPrev && prev) {
      meta.prev = prev;
    }

    return meta;
  }

  static createBasicPagination(page: number, limit: number): { skip: number; limit: number } {
    return {
      skip: (page - 1) * limit,
      limit
    };
  }

  static generateCursor(value: any): string {
    if (!value) return '';
    const sanitized = String(value).substring(0, 50);
    return Buffer.from(sanitized).toString('base64');
  }

  static validatePaginationSecurity(page: number, limit: number, endpoint?: string): void {
    const limits = this.getLimitsForEndpoint(endpoint);

    if (page > limits.maxPage) {
      throw new Error('Page number too high. Use cursor-based pagination for large datasets.');
    }

    if (limit > limits.maxLimit) {
      throw new Error(`Limit too high. Maximum ${limits.maxLimit} items per page allowed.`);
    }

    const offset = (page - 1) * limit;
    if (offset > 100000) {
      throw new Error('Offset too high. Use cursor-based pagination for better performance.');
    }
  }

  static shouldRateLimit(page: number, limit: number, endpoint?: string): boolean {
    const limits = this.getLimitsForEndpoint(endpoint);

    if (endpoint === 'contacts' || endpoint === 'contacts-all') {
      return page > 1000 || limit > limits.maxLimit || (page > 200 && limit > 100);
    }

    return page > 500 || limit > 100 || (page > 100 && limit > 50);
  }

  static createFacetPipeline(skip: number, limit: number): any[] {
    return [
      {
        $facet: {
          data: [
            { $skip: skip },
            { $limit: limit }
          ],
          totalCount: [
            { $count: 'count' }
          ]
        }
      },
      {
        $project: {
          data: 1,
          totalCount: { $arrayElemAt: ['$totalCount.count', 0] }
        }
      }
    ];
  }
}
