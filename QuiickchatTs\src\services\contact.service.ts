import { UserContacts, IContactItem } from '../models/contact.model';
import { User } from '../models/user.model';
import { DatabaseError } from '../utils/errors';
import { logger } from '../utils/logger';
import { PaginationUtil } from '../utils/pagination.util';
import {
  ContactInput,
  ContactResponse,
  BulkContactsResponse,
  ContactSyncResponse,
  PaginationQuery,
  PaginatedResponse
} from '../types';
import mongoose from 'mongoose';

export const normalizePhoneNumber = (phone: string): string => {
  return phone.replace(/[^\d+]/g, '');
};

export const checkRegistration = async (normalizedPhone: string): Promise<any> => {
  try {
    const user = await User.findOne({ phone: normalizedPhone });
    return user;
  } catch (error) {
    logger.error('Failed to check registration:', error);
    throw new DatabaseError(`Failed to check registration: ${error}`);
  }
};

// Helper function to process contacts in chunks
const processContactChunk = async (
  userContacts: any,
  contactsChunk: ContactInput[],
  now: Date
): Promise<{
  processedContacts: ContactResponse[];
  registeredCount: number;
  newContactsCount: number;
  updatedContactsCount: number;
}> => {
  const processedContacts: ContactResponse[] = [];
  let registeredCount = 0;
  let newContactsCount = 0;
  let updatedContactsCount = 0;

  for (const contactInput of contactsChunk) {
    const normalizedPhone = normalizePhoneNumber(contactInput.phone_number);

    if (!normalizedPhone) {
      continue;
    }

    const registeredUser = await checkRegistration(normalizedPhone);
    const isRegistered = !!registeredUser;
    const registeredUserId = registeredUser?._id?.toString();

    if (isRegistered) {
      registeredCount++;
    }

    const existingContactIndex = userContacts.contacts.findIndex(
      (c: IContactItem) => c.normalized_phone === normalizedPhone
    );

    const contactId = existingContactIndex >= 0 && userContacts.contacts[existingContactIndex]
      ? userContacts.contacts[existingContactIndex].contact_id
      : new mongoose.Types.ObjectId().toString();

    if (existingContactIndex >= 0 && userContacts.contacts[existingContactIndex]) {
      const existingContact = userContacts.contacts[existingContactIndex];
      existingContact.display_name = contactInput.display_name;
      existingContact.phone_number = contactInput.phone_number;
      existingContact.is_registered = isRegistered;
      existingContact.registered_user_id = registeredUserId;
      existingContact.updated_at = now;
      updatedContactsCount++;
    } else {
      const newContact: IContactItem = {
        contact_id: contactId,
        display_name: contactInput.display_name,
        phone_number: contactInput.phone_number,
        normalized_phone: normalizedPhone,
        is_registered: isRegistered,
        registered_user_id: registeredUserId,
        added_at: now,
        updated_at: now
      };
      userContacts.contacts.push(newContact);
      newContactsCount++;
    }

    const contactResponse: ContactResponse = {
      contact_id: contactId,
      display_name: contactInput.display_name,
      phone_number: contactInput.phone_number,
      is_registered: isRegistered,
      registered_user_id: registeredUserId,
      profile_picture: registeredUser?.profile_picture,
      status: registeredUser?.status,
      last_seen: registeredUser?.last_seen,
      added_at: now,
      updated_at: now
    };

    processedContacts.push(contactResponse);
  }

  return {
    processedContacts,
    registeredCount,
    newContactsCount,
    updatedContactsCount
  };
};

export const uploadContacts = async (userId: string, contacts: ContactInput[]): Promise<BulkContactsResponse> => {
  try {
    const now = new Date();
    const allProcessedContacts: ContactResponse[] = [];
    let totalRegisteredCount = 0;
    let totalNewContactsCount = 0;
    let totalUpdatedContactsCount = 0;

    // Define chunk size for processing (adjust based on performance needs)
    const CHUNK_SIZE = 500;
    const totalContacts = contacts.length;

    logger.info(`Starting chunked processing of ${totalContacts} contacts for user ${userId} (chunk size: ${CHUNK_SIZE})`);

    let userContacts = await UserContacts.findOne({ user_id: userId });

    if (!userContacts) {
      userContacts = new UserContacts({
        user_id: userId,
        contacts: [],
        total_contacts: 0,
        registered_contacts: 0
      });
    }

    // Process contacts in chunks
    for (let i = 0; i < totalContacts; i += CHUNK_SIZE) {
      const chunkStart = i;
      const chunkEnd = Math.min(i + CHUNK_SIZE, totalContacts);
      const contactsChunk = contacts.slice(chunkStart, chunkEnd);

      logger.info(`Processing chunk ${Math.floor(i / CHUNK_SIZE) + 1}/${Math.ceil(totalContacts / CHUNK_SIZE)}: contacts ${chunkStart + 1}-${chunkEnd}`);

      const chunkResult = await processContactChunk(userContacts, contactsChunk, now);

      // Accumulate results
      allProcessedContacts.push(...chunkResult.processedContacts);
      totalRegisteredCount += chunkResult.registeredCount;
      totalNewContactsCount += chunkResult.newContactsCount;
      totalUpdatedContactsCount += chunkResult.updatedContactsCount;

      // Save progress after each chunk to avoid memory issues and provide incremental updates
      userContacts.total_contacts = userContacts.contacts.length;
      userContacts.registered_contacts = userContacts.contacts.filter(c => c.is_registered).length;
      await userContacts.save();

      logger.info(`Chunk ${Math.floor(i / CHUNK_SIZE) + 1} completed: ${chunkResult.processedContacts.length} contacts processed`);

      // Add a small delay between chunks to prevent overwhelming the database
      if (chunkEnd < totalContacts) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Final save with updated totals
    userContacts.total_contacts = userContacts.contacts.length;
    userContacts.registered_contacts = userContacts.contacts.filter(c => c.is_registered).length;
    await userContacts.save();

    logger.info(`User ${userId} uploaded ${allProcessedContacts.length} contacts (${totalNewContactsCount} new, ${totalUpdatedContactsCount} updated, ${totalRegisteredCount} registered)`);

    const message = `${allProcessedContacts.length} contacts processed successfully in ${Math.ceil(totalContacts / CHUNK_SIZE)} chunks`;

    return {
      success: true,
      message,
      contacts_processed: allProcessedContacts.length,
      contacts_registered: totalRegisteredCount,
      contacts: allProcessedContacts
    };
  } catch (error) {
    logger.error('Failed to upload contacts:', error);
    throw new DatabaseError(`Failed to upload contacts: ${error}`);
  }
};

export const getUserContacts = async (
  userId: string,
  paginationQuery?: PaginationQuery,
  searchTerm?: string,
  registeredOnly?: boolean,
  showAll?: boolean
): Promise<PaginatedResponse<ContactResponse>> => {
  try {
    const { page, limit } = PaginationUtil.parsePaginationQuery(paginationQuery || {});
    const { skip } = PaginationUtil.createBasicPagination(page, limit);

    const matchStage: any = { user_id: userId };

    const additionalFilters: any[] = [];

    if (searchTerm) {
      additionalFilters.push({
        $match: {
          'contacts.display_name': { $regex: searchTerm, $options: 'i' }
        }
      });
    }

    if (registeredOnly) {
      additionalFilters.push({
        $match: {
          'contacts.is_registered': true
        }
      });
    }

    const pipeline = [
      { $match: matchStage },
      { $unwind: '$contacts' },
      ...additionalFilters,
      {
        $lookup: {
          from: 'users',
          let: { registeredUserId: '$contacts.registered_user_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: [{ $toString: '$_id' }, '$$registeredUserId']
                }
              }
            },
            {
              $project: {
                profile_picture: 1,
                status: 1,
                last_seen: 1
              }
            }
          ],
          as: 'user_details'
        }
      },
      {
        $addFields: {
          'contacts.user_details': { $arrayElemAt: ['$user_details', 0] }
        }
      },
      { $sort: { 'contacts.display_name': 1 } },
      { $replaceRoot: { newRoot: '$contacts' } },
      ...PaginationUtil.createFacetPipeline(skip, limit)
    ];

    const [result] = await UserContacts.aggregate(pipeline);

    if (!result) {
      return {
        success: true,
        message: 'No contacts found',
        data: [],
        pagination: PaginationUtil.calculatePaginationMeta(page, limit, 0)
      };
    }

    const { data = [], totalCount = 0 } = result;

    let transformedContacts: ContactResponse[] = data.map((contact: any) => ({
      contact_id: contact.contact_id,
      display_name: contact.display_name,
      phone_number: contact.phone_number,
      is_registered: contact.is_registered,
      registered_user_id: contact.registered_user_id,
      profile_picture: contact.user_details?.profile_picture,
      status: contact.user_details?.status,
      last_seen: contact.user_details?.last_seen,
      added_at: contact.added_at,
      updated_at: contact.updated_at
    }));

    if (showAll) {
      const registeredContacts = transformedContacts.filter(c => c.is_registered);
      const unregisteredContacts = transformedContacts.filter(c => !c.is_registered);
      transformedContacts = [...registeredContacts, ...unregisteredContacts];
    }

    const paginationMeta = PaginationUtil.calculatePaginationMeta(
      page,
      limit,
      totalCount
    );

    return {
      success: true,
      message: `Retrieved ${transformedContacts.length} contacts`,
      data: transformedContacts,
      pagination: paginationMeta
    };
  } catch (error) {
    logger.error('Failed to get user contacts:', error);
    throw new DatabaseError(`Failed to get user contacts: ${error}`);
  }
};

// Legacy function for backward compatibility
export const getUserContactsLegacy = async (userId: string): Promise<ContactSyncResponse> => {
  try {
    const result = await getUserContacts(userId, { page: 1, limit: 1000 });

    const registeredCount = result.data.filter(contact => contact.is_registered).length;

    return {
      success: true,
      contacts: result.data,
      total_contacts: result.pagination.total,
      registered_contacts: registeredCount
    };
  } catch (error) {
    logger.error('Failed to get user contacts (legacy):', error);
    throw new DatabaseError(`Failed to get user contacts: ${error}`);
  }
};

export const getRegisteredContacts = async (userId: string): Promise<string[]> => {
  try {
    const userContacts = await UserContacts.findOne({ user_id: userId });
    
    if (!userContacts) {
      return [];
    }

    return userContacts.contacts
      .filter(contact => contact.is_registered && contact.registered_user_id)
      .map(contact => contact.registered_user_id!)
      .filter(id => id);
  } catch (error) {
    logger.error('Failed to get registered contacts:', error);
    throw new DatabaseError(`Failed to get registered contacts: ${error}`);
  }
};
